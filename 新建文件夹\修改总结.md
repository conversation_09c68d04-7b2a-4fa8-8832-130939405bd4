# 振幅显示模式通用化修改总结

## 修改目标
将程序中的振幅显示模式设定为检波器和DAS通用，确保所有6种振幅处理模式在三种绘图方式中都能一致工作。

## 主要修改内容

### 1. 配置统一化
- 在配置部分添加了 `config.amplitude_mode = amplitude_mode;`
- 确保所有绘图函数都能访问到统一的振幅模式配置
- 更新了注释，明确说明振幅模式对检波器和DAS都生效

### 2. 创建通用振幅处理函数
- 新增 `apply_amplitude_mode()` 函数，实现统一的振幅处理逻辑
- 支持所有6种振幅模式：
  - 模式1: 传统归一化
  - 模式2: 轻微保留振幅差异 (30%)
  - 模式3: 中等保留振幅差异 (50%)
  - 模式4: 强烈保留振幅差异 (80%)
  - 模式5: 不进行归一化
  - 模式6: 第一道显示原波形，其余道归一化

### 3. 修改检波器绘图函数
- 将原有的振幅处理逻辑替换为调用 `apply_amplitude_mode()` 函数
- 保持了检波器特定的振幅缩放 (`config.amplitude_scale`)

### 4. 修改DAS绘图函数
- 更新函数签名，添加 `first_trace_amplitude_factor` 参数
- 将原有的简单归一化替换为通用振幅处理
- 保持了DAS特定的振幅缩放 (`config.das_amplitude_scale`)

### 5. 修改对比绘图函数
- 检波器和DAS数据都使用统一的振幅处理函数
- 确保两种数据在对比时使用相同的振幅处理逻辑

### 6. 更新函数调用
- 修改了调用DAS绘图函数的地方，传递必要的参数
- 确保所有函数调用都包含正确的参数

## 技术实现细节

### 通用振幅处理函数特点
- **输入参数**:
  - `trace_data`: 原始道数据
  - `trace_index`: 道索引（从1开始）
  - `amplitude_mode`: 振幅模式（1-6）
  - `first_trace_amplitude_factor`: 第一道振幅倍数
- **输出**: 处理后的道数据
- **特殊处理**: 模式6中第一道有特殊的振幅放大处理

### 配置管理
- 振幅模式通过 `config.amplitude_mode` 统一管理
- 所有绘图函数都能访问到相同的配置
- 保持了原有的其他配置参数不变

## 验证和测试

### 1. 创建了测试脚本
- `test_amplitude_modes.m`: 验证所有振幅模式的工作状态
- 测试了所有6种模式的处理效果
- 验证了模式6的特殊行为（第一道振幅放大20倍）

### 2. 语法检查
- 通过了MATLAB语法检查
- 没有发现语法错误或警告

## 使用方法

### 修改振幅模式
在 `huitu_best.m` 文件中修改：
```matlab
amplitude_mode = 6;  % 改为1-6中的任意数字
```

### 调整第一道振幅倍数（模式6专用）
```matlab
first_trace_amplitude_factor = 20.0;  % 调整倍数
```

## 优势

1. **一致性**: 检波器和DAS使用完全相同的振幅处理逻辑
2. **可维护性**: 振幅处理逻辑集中在一个函数中，便于维护
3. **灵活性**: 6种模式满足不同的分析需求
4. **兼容性**: 保持了原有的其他功能不变

## 文件清单

- `huitu_best.m`: 主程序文件（已修改）
- `apply_amplitude_mode.m`: 独立的振幅处理函数
- `test_amplitude_modes.m`: 测试脚本
- `振幅模式使用说明.md`: 详细使用说明
- `修改总结.md`: 本文件

修改完成，所有振幅显示模式现已设定为检波器和DAS通用！
