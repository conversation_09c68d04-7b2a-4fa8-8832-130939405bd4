# DAS与检波器绘图程序使用说明

## 概述
`huitu_best.m` 程序已升级，现在支持三种绘图模式：
1. 仅绘制检波器波形（原功能）
2. 仅绘制DAS波形
3. 绘制DAS和检波器叠加对比波形

## 主要配置参数

### 绘图模式选择
```matlab
plot_mode = 1;  % 绘图模式选择：
                % 1 - 仅绘制检波器波形（原功能）
                % 2 - 仅绘制DAS波形
                % 3 - 绘制DAS和检波器叠加对比波形（黑色=检波器，红色=DAS）
```

### 数据文件配置
```matlab
data_filename = 'FDTD_SeismicLogging_20250729_152710.mat';  % 检波器数据文件
das_data_filename = 'das_simulation_results.mat';           % DAS数据文件
```

### DAS显示配置
```matlab
config.das_num_channels_to_show = 21;  % 每炮显示的DAS通道数量
config.das_channel_start = 1;          % 起始DAS通道编号
config.das_channel_spacing = 0.015;    % DAS通道间距（米）
```

### DAS显示参数
```matlab
config.das_trace_spacing = 1.5;       % DAS道间距（垂直偏移）
config.das_amplitude_scale = 0.8;     % DAS振幅缩放因子
config.das_fill_positive = true;      % 是否填充DAS正振幅
config.das_fill_color = [0.2, 0.2, 0.8]; % DAS填充颜色（蓝色）
```

### 叠加对比模式配置
```matlab
config.receiver_line_color = [0, 0, 0];    % 检波器波形颜色（黑色）
config.das_line_color = [1, 0, 0];         % DAS波形颜色（红色）
config.receiver_line_width = 1.2;          % 检波器线宽
config.das_line_width = 1.0;               % DAS线宽
```

## 使用步骤

### 1. 准备数据文件
- 检波器数据：`FDTD_SeismicLogging_20250729_152710.mat`
- DAS数据：`das_simulation_results.mat`

确保DAS数据文件包含名为 `das_data` 或 `data` 的数据矩阵。

### 2. 选择绘图模式
修改 `plot_mode` 参数：
- `plot_mode = 1`：仅显示检波器数据
- `plot_mode = 2`：仅显示DAS数据  
- `plot_mode = 3`：显示叠加对比图

### 3. 配置显示参数
根据需要调整：
- 显示的通道/检波器数量
- 起始通道/检波器编号
- 振幅缩放因子
- 颜色和线宽设置

### 4. 运行程序
```matlab
run('huitu_best.m')
```

## 输出文件
程序会在 `Picture_AcousticLogging` 文件夹中生成图片：
- 模式1：`检波器_炮点_X.png`
- 模式2：`DAS_炮点_X.png`
- 模式3：`对比_炮点_X.png`

## 数据格式要求

### 检波器数据格式
- 矩阵尺寸：`[炮点数 × (检波器数 × 时间采样点数)]`
- 每个检波器的数据按时间顺序排列

### DAS数据格式
- 矩阵尺寸：`[炮点数 × (DAS通道数 × 时间采样点数)]`
- 每个DAS通道的数据按时间顺序排列
- 时间采样点数应与检波器数据一致

## 注意事项

1. **数据一致性**：确保DAS数据和检波器数据的时间采样点数相同
2. **文件路径**：确保数据文件在当前工作目录中
3. **内存使用**：大数据集可能需要较多内存
4. **参数调整**：根据实际数据特征调整振幅缩放和显示参数

## 故障排除

### 常见错误及解决方法

1. **"DAS数据加载失败"**
   - 检查DAS数据文件路径和文件名
   - 确认文件中包含有效的数据矩阵

2. **"DAS数据格式不匹配"**
   - 检查DAS数据矩阵的列数是否为时间采样点数的整数倍
   - 确认时间采样点数与检波器数据一致

3. **"数据索引超出范围"**
   - 检查显示的通道数量设置
   - 确认起始通道编号不超过实际通道数

## 示例配置

### 显示前10个通道的对比图
```matlab
plot_mode = 3;
config.num_receivers_to_show = 10;
config.receiver_start = 1;
config.das_num_channels_to_show = 10;
config.das_channel_start = 1;
```

### 显示中间10个通道的DAS数据
```matlab
plot_mode = 2;
config.das_num_channels_to_show = 10;
config.das_channel_start = 6;
```
