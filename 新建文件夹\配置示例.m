% DAS绘图程序配置示例
% 展示不同使用场景的配置方法

%% ==================== 示例1：仅绘制检波器波形 ====================
% 这是原始功能，保持不变
fprintf('=== 示例1：仅绘制检波器波形 ===\n');

% 基本配置
plot_mode = 1;  % 仅检波器
data_filename = 'FDTD_SeismicLogging_20250729_152710.mat';
shot_numbers = [5, 10, 20, 30, 35, 40];

% 检波器显示配置
config.num_receivers_to_show = 21;
config.receiver_start = 1;

% 显示参数
config.trace_spacing = 1.5;
config.amplitude_scale = 0.8;
config.fill_positive = true;
config.fill_color = [0.8, 0.2, 0.2];

fprintf('配置完成：将生成检波器_炮点_X.png文件\n\n');

%% ==================== 示例2：仅绘制DAS波形 ====================
fprintf('=== 示例2：仅绘制DAS波形 ===\n');

% 基本配置
plot_mode = 2;  % 仅DAS
data_filename = 'FDTD_SeismicLogging_20250729_152710.mat';  % 仍需要用于参数
das_data_filename = 'das_simulation_results.mat';
shot_numbers = [5, 10, 20, 30, 35, 40];

% DAS显示配置
config.das_num_channels_to_show = 21;
config.das_channel_start = 1;
config.das_channel_spacing = 0.015;

% DAS显示参数
config.das_trace_spacing = 1.5;
config.das_amplitude_scale = 0.8;
config.das_fill_positive = true;
config.das_fill_color = [0.2, 0.2, 0.8];  % 蓝色

fprintf('配置完成：将生成DAS_炮点_X.png文件\n\n');

%% ==================== 示例3：DAS与检波器叠加对比 ====================
fprintf('=== 示例3：DAS与检波器叠加对比 ===\n');

% 基本配置
plot_mode = 3;  % 叠加对比
data_filename = 'FDTD_SeismicLogging_20250729_152710.mat';
das_data_filename = 'das_simulation_results.mat';
shot_numbers = [5, 10, 20, 30, 35, 40];

% 检波器显示配置
config.num_receivers_to_show = 15;  % 显示15个检波器
config.receiver_start = 1;

% DAS显示配置
config.das_num_channels_to_show = 15;  % 显示15个DAS通道
config.das_channel_start = 1;

% 叠加对比显示参数
config.trace_spacing = 1.5;
config.amplitude_scale = 0.8;
config.das_amplitude_scale = 0.8;

% 颜色和线宽设置
config.receiver_line_color = [0, 0, 0];    % 黑色检波器
config.das_line_color = [1, 0, 0];         % 红色DAS
config.receiver_line_width = 1.2;
config.das_line_width = 1.0;

fprintf('配置完成：将生成对比_炮点_X.png文件\n');
fprintf('图例：黑色线=检波器，红色线=DAS\n\n');

%% ==================== 示例4：显示特定深度范围 ====================
fprintf('=== 示例4：显示特定深度范围 ===\n');

% 显示中间10个检波器和DAS通道的对比
plot_mode = 3;
config.num_receivers_to_show = 10;
config.receiver_start = 6;  % 从第6个检波器开始
config.das_num_channels_to_show = 10;
config.das_channel_start = 6;  % 从第6个DAS通道开始

fprintf('配置完成：将显示第6-15个检波器和DAS通道的对比\n\n');

%% ==================== 示例5：高质量图片输出 ====================
fprintf('=== 示例5：高质量图片输出 ===\n');

% 高分辨率配置
config.dpi = 600;  % 超高分辨率
config.figure_size = [1600, 1200];  % 更大的图形尺寸

% 精细显示参数
config.trace_spacing = 2.0;  % 增大道间距
config.amplitude_scale = 1.0;  % 增大振幅
config.receiver_line_width = 1.5;  % 增加线宽
config.das_line_width = 1.2;

fprintf('配置完成：将生成600 DPI高质量图片\n\n');

%% ==================== 示例6：第一道特殊显示模式 ====================
fprintf('=== 示例6：第一道特殊显示模式 ===\n');

% 使用振幅模式6（第一道显示原波形）
amplitude_mode = 6;
first_trace_amplitude_factor = 20.0;  % 第一道振幅倍数

% 适配第一道的显示范围
config.trace_spacing = 1.5;

fprintf('配置完成：第一道将显示原始振幅特征\n\n');

%% ==================== 示例7：时间窗口选择 ====================
fprintf('=== 示例7：时间窗口选择 ===\n');

% 显示特定时间范围
config.time_range = [0.001, 0.003];  % 显示1-3毫秒

% 或者显示全部时间
% config.time_range = [];  % 空值表示显示全部时间

fprintf('配置完成：将显示1-3毫秒时间窗口\n\n');

%% ==================== 示例8：批量处理不同炮点 ====================
fprintf('=== 示例8：批量处理不同炮点 ===\n');

% 处理更多炮点
shot_numbers = [1:5:67];  % 每隔5个炮点处理一次

% 或者处理特定炮点
% shot_numbers = [10, 20, 30, 40, 50];

fprintf('配置完成：将处理炮点 ');
fprintf('%d ', shot_numbers);
fprintf('\n\n');

%% ==================== 使用提示 ====================
fprintf('=== 使用提示 ===\n');
fprintf('1. 将上述任一示例的配置复制到 huitu_best.m 的配置区域\n');
fprintf('2. 根据实际数据调整参数\n');
fprintf('3. 运行 huitu_best.m\n');
fprintf('4. 检查 Picture_AcousticLogging 文件夹中的输出图片\n\n');

fprintf('=== 常用参数调整建议 ===\n');
fprintf('• 如果波形太密集：增大 trace_spacing\n');
fprintf('• 如果振幅太小：增大 amplitude_scale\n');
fprintf('• 如果需要更清晰的线条：增大 line_width\n');
fprintf('• 如果需要高质量打印：增大 dpi 到 600 或更高\n');
fprintf('• 如果内存不足：减少 num_receivers_to_show 或处理更少炮点\n');
