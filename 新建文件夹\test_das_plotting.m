% DAS绘图功能测试脚本
% 用于验证新增的DAS绘图功能是否正常工作

%% 清理工作空间
clear; clc; close all;

%% 检查必要的数据文件是否存在
fprintf('=== DAS绘图功能测试 ===\n');

% 检查检波器数据文件
receiver_file = 'FDTD_SeismicLogging_20250729_152710.mat';
if exist(receiver_file, 'file')
    fprintf('✓ 检波器数据文件存在: %s\n', receiver_file);
else
    fprintf('✗ 检波器数据文件不存在: %s\n', receiver_file);
    fprintf('请确保数据文件在当前目录中\n');
    return;
end

% 检查DAS数据文件
das_file = 'das_simulation_results.mat';
if exist(das_file, 'file')
    fprintf('✓ DAS数据文件存在: %s\n', das_file);
    test_das = true;
else
    fprintf('✗ DAS数据文件不存在: %s\n', das_file);
    fprintf('将仅测试检波器绘图功能\n');
    test_das = false;
end

%% 测试检波器绘图功能（模式1）
fprintf('\n--- 测试模式1：检波器绘图 ---\n');
try
    % 临时修改huitu_best.m中的配置
    fprintf('测试检波器绘图功能...\n');
    
    % 这里可以通过修改全局变量或者创建临时配置来测试
    % 由于原程序使用脚本形式，我们建议用户手动修改plot_mode进行测试
    
    fprintf('请手动设置 plot_mode = 1 并运行 huitu_best.m 来测试检波器绘图\n');
    
catch ME
    fprintf('检波器绘图测试失败: %s\n', ME.message);
end

%% 测试DAS绘图功能（模式2）
if test_das
    fprintf('\n--- 测试模式2：DAS绘图 ---\n');
    try
        fprintf('请手动设置 plot_mode = 2 并运行 huitu_best.m 来测试DAS绘图\n');
    catch ME
        fprintf('DAS绘图测试失败: %s\n', ME.message);
    end
    
    %% 测试叠加对比功能（模式3）
    fprintf('\n--- 测试模式3：叠加对比绘图 ---\n');
    try
        fprintf('请手动设置 plot_mode = 3 并运行 huitu_best.m 来测试叠加对比绘图\n');
    catch ME
        fprintf('叠加对比绘图测试失败: %s\n', ME.message);
    end
end

%% 数据格式检查
fprintf('\n--- 数据格式检查 ---\n');

% 检查检波器数据格式
try
    receiver_data = load(receiver_file);
    if isfield(receiver_data, 'data')
        fprintf('检波器数据矩阵尺寸: %d × %d\n', size(receiver_data.data, 1), size(receiver_data.data, 2));
        
        % 检查关键参数
        if isfield(receiver_data, 'maxt')
            fprintf('时间采样点数 (maxt): %d\n', receiver_data.maxt);
        end
        if isfield(receiver_data, 'num_receivers') || isfield(receiver_data, 'N')
            if isfield(receiver_data, 'num_receivers')
                num_recv = receiver_data.num_receivers;
            else
                num_recv = receiver_data.N;
            end
            fprintf('检波器数量: %d\n', num_recv);
        end
    else
        fprintf('警告: 检波器数据文件中未找到 data 字段\n');
    end
catch ME
    fprintf('检波器数据格式检查失败: %s\n', ME.message);
end

% 检查DAS数据格式
if test_das
    try
        das_data_loaded = load(das_file);
        
        % 查找DAS数据矩阵
        das_matrix = [];
        if isfield(das_data_loaded, 'das_data')
            das_matrix = das_data_loaded.das_data;
            fprintf('找到DAS数据字段: das_data\n');
        elseif isfield(das_data_loaded, 'data')
            das_matrix = das_data_loaded.data;
            fprintf('找到DAS数据字段: data\n');
        else
            % 查找第一个数值矩阵
            fields = fieldnames(das_data_loaded);
            for i = 1:length(fields)
                if isnumeric(das_data_loaded.(fields{i})) && ismatrix(das_data_loaded.(fields{i}))
                    das_matrix = das_data_loaded.(fields{i});
                    fprintf('找到DAS数据字段: %s\n', fields{i});
                    break;
                end
            end
        end
        
        if ~isempty(das_matrix)
            fprintf('DAS数据矩阵尺寸: %d × %d\n', size(das_matrix, 1), size(das_matrix, 2));
            
            % 检查数据一致性
            if exist('receiver_data', 'var') && isfield(receiver_data, 'maxt')
                expected_cols = size(das_matrix, 2) / receiver_data.maxt;
                if mod(size(das_matrix, 2), receiver_data.maxt) == 0
                    fprintf('DAS通道数: %d\n', expected_cols);
                    fprintf('✓ DAS数据格式与检波器数据兼容\n');
                else
                    fprintf('✗ DAS数据格式与检波器数据不兼容\n');
                    fprintf('DAS数据列数应该是时间采样点数的整数倍\n');
                end
            end
        else
            fprintf('✗ DAS数据文件中未找到有效的数据矩阵\n');
        end
        
    catch ME
        fprintf('DAS数据格式检查失败: %s\n', ME.message);
    end
end

%% 配置建议
fprintf('\n--- 配置建议 ---\n');
fprintf('1. 根据数据检查结果，调整以下参数:\n');
fprintf('   - config.num_receivers_to_show (检波器显示数量)\n');
fprintf('   - config.das_num_channels_to_show (DAS通道显示数量)\n');
fprintf('2. 选择合适的plot_mode:\n');
fprintf('   - plot_mode = 1: 仅检波器\n');
fprintf('   - plot_mode = 2: 仅DAS\n');
fprintf('   - plot_mode = 3: 叠加对比\n');
fprintf('3. 调整显示参数以获得最佳效果\n');

fprintf('\n=== 测试完成 ===\n');
fprintf('请根据上述检查结果修改 huitu_best.m 中的配置参数\n');
