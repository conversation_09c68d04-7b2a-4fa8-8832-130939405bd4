# 振幅显示模式使用说明

## 概述
程序中的振幅显示模式现已设定为检波器和DAS通用，所有6种模式在三种绘图方式中都能正常使用：
1. 仅检波器绘图
2. 仅DAS绘图  
3. 检波器与DAS对比绘图

## 振幅模式详细说明

### 模式1 - 传统归一化
- **特点**: 所有道振幅相同，清晰显示波形形状
- **适用**: 需要清楚看到每道波形细节时
- **效果**: 每道都归一化到相同的最大振幅

### 模式2 - 轻微保留振幅差异
- **特点**: 在清晰显示和保留振幅信息间取平衡
- **适用**: 既要看清波形又要保留部分振幅关系
- **效果**: 30%保留原始振幅差异，70%归一化处理

### 模式3 - 中等保留振幅差异
- **特点**: 中等程度保留振幅差异
- **适用**: 需要较多振幅信息的分析
- **效果**: 50%保留原始振幅差异，50%归一化处理

### 模式4 - 强烈保留振幅差异
- **特点**: 强烈保留原始振幅关系
- **适用**: 振幅分析为主要目的时
- **效果**: 80%保留原始振幅差异，20%归一化处理

### 模式5 - 不进行归一化
- **特点**: 完全显示原始波形振幅
- **适用**: 需要真实振幅信息时
- **效果**: 完全保持原始数据，可能导致小振幅道不可见

### 模式6 - 第一道显示原波形
- **特点**: 第一道显示原始振幅（放大显示），其余道归一化
- **适用**: 需要突出显示第一道的情况
- **效果**: 第一道振幅放大20倍显示，其余道正常归一化

## 使用方法

### 1. 修改振幅模式
在 `huitu_best.m` 文件中找到以下行：
```matlab
amplitude_mode = 6;  % 请修改这个数字来选择模式（对检波器和DAS都生效）
```

将数字改为1-6中的任意一个即可。

### 2. 调整第一道振幅倍数（仅模式6）
如果使用模式6，可以调整第一道的显示倍数：
```matlab
first_trace_amplitude_factor = 20.0;  % 第一道相对于其他道的振幅倍数
```

## 技术实现

### 通用振幅处理函数
所有绘图函数现在都调用统一的 `apply_amplitude_mode()` 函数，确保：
- 检波器绘图使用相同的振幅处理
- DAS绘图使用相同的振幅处理  
- 对比绘图中两种数据使用相同的振幅处理
- 处理逻辑完全一致，避免不同绘图方式间的差异

### 配置统一管理
振幅模式通过 `config.amplitude_mode` 统一管理，所有绘图函数都能访问到相同的配置。

## 注意事项

1. **模式选择**: 根据分析目的选择合适的模式
2. **数据特性**: 不同模式适合不同的数据特性
3. **显示效果**: 可以通过运行程序查看不同模式的实际效果
4. **通用性**: 所有模式在检波器和DAS绘图中表现一致

## 测试验证

可以运行 `test_amplitude_modes.m` 来验证所有振幅模式的工作状态。
