# DAS集成声波测井正演模拟程序

## 概述

本程序基于原始的`best_main.m`声波测井正演模拟程序，集成了分布式声学传感(DAS)系统，实现了DAS与传统检波器的同时数据采集和对比分析。

## 主要特点

### 1. DAS系统集成
- **同位置部署**: DAS标距点与检波器部署在完全相同的位置
- **优化参数**: 标距长度0.6米，重叠率70%，确保最佳信噪比
- **应变率测量**: 基于光纤两端速度差计算应变率，模拟真实DAS工作原理

### 2. 数据采集增强
- **双重采集**: 同时采集检波器和DAS数据
- **完整存储**: 保存所有系统参数和配置信息
- **数据对比**: 便于后续分析和对比研究

### 3. 可视化功能
- **实时对比**: 程序运行时生成DAS与检波器数据对比图
- **多维分析**: 包含时域、频域和统计分析
- **自动保存**: 所有图表和数据自动保存

## 文件结构

```
新建文件夹/
├── best_main.m                 # 主程序（已集成DAS）
├── zhengyan.m                  # 参考程序（原DAS实现）
├── test_das_integration.m      # DAS集成测试脚本
├── analyze_das_data.m          # 数据分析脚本
└── README_DAS_Integration.md   # 本说明文件
```

## 使用方法

### 1. 基本运行

```matlab
% 直接运行主程序
run('best_main.m');
```

程序将：
- 同时模拟67炮声波测井
- 在每炮部署21个检波器和21个DAS标距点
- 自动保存数据和对比图

### 2. 测试集成功能

```matlab
% 运行集成测试
run('test_das_integration.m');
```

### 3. 数据分析

```matlab
% 分析生成的数据
analyze_das_data();  % 自动找到最新数据文件

% 或指定特定文件
analyze_das_data('FDTD_SeismicLogging_20250730_143022.mat');
```

## 程序配置

### 主要参数设置

```matlab
% 检波器配置
N = 21;                    % 检波器数量
L_StoR = 1.5;             % 源距 (米)
L_RtoR = 0.15;            % 检波器间距 (米)

% DAS系统配置
num_das_points = 21;       % DAS标距点数量（与检波器相同）
gauge_length = 0.6;        % 标距长度 (米)
gauge_overlap = 0.7;       % 标距重合比例 (70%)
```

### 炮数控制

```matlab
shot_start = 1;            % 起始炮号
shot_end = 67;             % 结束炮号（可调整用于测试）
```

## 数据结构

### 输出数据

程序生成的`.mat`文件包含以下主要变量：

#### 检波器数据
- `data`: 检波器数据矩阵 [炮数 × (道数×时间点)]
- `X`: 最后一炮的检波器数据 [道数 × 时间点]

#### DAS数据
- `das_data`: DAS数据矩阵 [炮数 × (标距点×时间点)]
- `das_strain_rate`: 最后一炮的DAS应变率数据 [标距点 × 时间点]

#### 系统参数
- `gauge_length`: 标距长度
- `gauge_overlap`: 标距重合比例
- `gauge_centers`: 标距中心点位置
- `num_das_points`: DAS标距点数量

#### 模型参数
- `vp`, `vs`, `dens`: 速度和密度分布
- `dx`, `dz`, `dt`: 网格和时间参数
- `f0`: 震源主频

## 数据分析功能

### 1. 单炮对比分析
- 检波器与DAS波形对比
- 频谱分析
- 多道/多标距点显示

### 2. 多炮汇总分析
- 所有炮数据概览
- 统计特性对比
- 信噪比分析

### 3. 自动报告生成
- 系统配置总结
- 数据质量评估
- 优化建议

## 技术实现

### DAS应变率计算

```matlab
% 计算标距两端的速度差
v_start = Vz(gauge_start, med_x);
v_end = Vz(gauge_end, med_x);

% 应变率 = 速度差 / 标距长度
strain_rate = (v_end - v_start) / actual_distance;
```

### 数据同步采集

程序在每个时间步同时采集：
1. 检波器数据（应力场）
2. DAS应变率数据（速度场导数）

确保两种数据的时间同步和空间对应。

## 注意事项

1. **内存需求**: DAS数据增加了存储需求，建议确保足够内存
2. **计算时间**: 双重数据采集会略微增加计算时间
3. **参数调优**: 可根据具体需求调整标距长度和重合比例
4. **数据解释**: DAS测量应变率，检波器测量应力，物理意义不同

## 故障排除

### 常见问题

1. **内存不足**
   - 减少炮数或时间采样点
   - 使用`shot_start`和`shot_end`控制运行范围

2. **数据异常**
   - 检查DAS参数设置
   - 验证标距长度是否合理

3. **图形显示问题**
   - 确保MATLAB图形环境正常
   - 检查文件保存权限

## 更新日志

- **v1.0**: 初始版本，集成基本DAS功能
- **v1.1**: 添加数据分析和可视化功能
- **v1.2**: 优化参数设置和错误处理

## 联系信息

如有问题或建议，请联系开发团队。

---

*本程序基于FDTD方法实现二维弹性波正演模拟，集成DAS系统用于声波测井应用研究。*
