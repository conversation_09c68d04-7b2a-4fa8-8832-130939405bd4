% 声波测井标准显示程序
% 实现类似您展示图片的声波测井波形显示效果

%% ==================== 用户配置区域 ====================
% 1. 数据文件选择
data_filename = 'FDTD_SeismicLogging_20250729_152710.mat';

% 2. 要显示的炮点选择
shot_numbers = [5, 10, 20,30, 35, 40, 45,50, 60, 67]; % 测试炮点

% 3. 检波器显示配置
config.num_receivers_to_show = 21;  % 每炮显示的检波器数量（1-21）
config.receiver_start = 1;          % 起始检波器编号（1-21）
% 示例：显示前10个检波器：num_receivers_to_show=10, receiver_start=1
% 示例：显示中间10个检波器：num_receivers_to_show=10, receiver_start=6
% 示例：显示后10个检波器：num_receivers_to_show=10, receiver_start=12

% 4. 声波测井显示参数配置
config.trace_spacing = 1.5;        % 道间距（垂直偏移）
config.amplitude_scale = 0.8;      % 振幅缩放因子
config.show_first_arrivals = false; % 是否标注首波到达
config.time_range = [0, 0.004];    % 显示时间范围[开始, 结束]秒
config.fill_positive = true;       % 是否填充正振幅
config.fill_color = [0.8, 0.2, 0.2]; % 填充颜色 [R, G, B]

% 5. 图片质量配置
config.dpi = 400;                  % 图片分辨率 (300 DPI高清)
config.figure_size = [1200, 900];  % 图形窗口大小 [宽, 高]

% 振幅显示模式选择（修改amplitude_mode的值）：
% 1 - 传统归一化（所有道振幅相同，清晰显示）
% 2 - 轻微保留振幅差异
% 3 - 中等保留振幅差异
% 4 - 强烈保留振幅差异
% 5 - 不进行归一化，显示原波形
% 6 - 第一道显示原波形，其余道归一化显示
amplitude_mode = 6;  % 请修改这个数字来选择模式

% 模式6专用参数：第一道相对于其他道的振幅倍数（参考例子图片的比例）
first_trace_amplitude_factor = 20.0;

% 根据选择的模式设置参数
switch amplitude_mode
    case 1
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
    case 2
        config.preserve_amplitude = true;
        config.amplitude_balance = 0.3;
    case 3
        config.preserve_amplitude = true;
        config.amplitude_balance = 0.5;
    case 4
        config.preserve_amplitude = true;
        config.amplitude_balance = 0.8;
    case 5
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
        config.no_normalization = true;  % 新增标志：不进行归一化
    case 6
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
        config.first_trace_original = true;  % 新增标志：第一道显示原波形
    otherwise
        warning('无效的amplitude_mode值，使用默认模式1');
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
end

% 为其他模式设置默认值
if amplitude_mode ~= 5
    config.no_normalization = false;
end
if amplitude_mode ~= 6
    config.first_trace_original = false;
end

%% ==================== 数据加载 ====================
% 加载数据文件
if ~exist('data', 'var') || ~exist('X', 'var')
    current_folder = pwd;
    full_data_path = fullfile(current_folder, data_filename);
    
    try
        load(full_data_path);
        fprintf('成功加载数据文件: %s\n', full_data_path);
    catch ME
        error('数据加载失败: %s', ME.message);
    end
end

% 设置默认参数
if ~exist('num_receivers', 'var')
    if exist('N', 'var')
        num_receivers = N;
    else
        num_receivers = 21;
    end
end

if ~exist('dz', 'var')
    dz = 0.015;
end

if ~exist('pml', 'var')
    pml = 50;
end

if ~exist('nz', 'var')
    nz = 2*pml+1200;
end

if ~exist('len_RtoR', 'var')
    len_RtoR = 10;
end

if ~exist('num_s', 'var')
    num_s = 67;
end

if ~exist('f0', 'var')
    f0 = 10000; % 10 kHz
end

if ~exist('L_RtoR', 'var')
    L_RtoR = 0.15; % 0.15米
end

% 添加缺失的关键变量
if ~exist('len_StoR', 'var')
    if exist('L_StoR', 'var')
        len_StoR = round(L_StoR / dz);  % 从物理距离计算网格点数
    else
        L_StoR = 1.5;  % 默认源距1.5米
        len_StoR = round(L_StoR / dz);
    end
end

% 确保时间相关变量存在
if ~exist('maxt', 'var')
    maxt = 2000;  % 默认时间采样点数
end

if ~exist('dt', 'var')
    if exist('f0', 'var')
        dt = 1/(20*f0);  % 根据频率估算时间步长
    else
        dt = 5e-6;  % 默认时间步长 5微秒
    end
end

%% ==================== 主处理循环 ====================
% 创建Picture文件夹
picture_dir = fullfile(pwd, 'Picture_AcousticLogging');
if ~exist(picture_dir, 'dir')
    mkdir(picture_dir);
    fprintf('创建Picture文件夹: %s\n', picture_dir);
end

% 数据完整性检查
fprintf('\n==================== 数据完整性检查 ====================\n');
fprintf('数据矩阵尺寸: %d × %d\n', size(data, 1), size(data, 2));
fprintf('预期数据尺寸: %d × %d\n', num_s, num_receivers * maxt);
fprintf('关键参数: maxt=%d, dt=%.2e, num_receivers=%d\n', maxt, dt, num_receivers);
fprintf('几何参数: len_StoR=%d, len_RtoR=%d, dz=%.4f\n', len_StoR, len_RtoR, dz);

% 检查数据矩阵尺寸是否匹配
expected_cols = num_receivers * maxt;
if size(data, 2) ~= expected_cols
    warning('数据矩阵列数不匹配！实际: %d, 预期: %d', size(data, 2), expected_cols);
    % 尝试自动修正
    if size(data, 2) > expected_cols
        fprintf('截取前%d列数据\n', expected_cols);
        data = data(:, 1:expected_cols);
    else
        error('数据矩阵列数不足，无法继续处理');
    end
end

% 创建时间向量
time_vec = (1:maxt) * dt;

% 确定时间显示范围
if isempty(config.time_range)
    time_indices = 1:maxt;
    display_time = time_vec;
else
    time_indices = find(time_vec >= config.time_range(1) & time_vec <= config.time_range(2));
    if isempty(time_indices)
        time_indices = 1:maxt;
    end
    display_time = time_vec(time_indices);
end

% 处理每个炮点
for shot_idx = 1:length(shot_numbers)
    count_s = shot_numbers(shot_idx);
    
    % 检查炮点编号是否有效
    fprintf('调试: 检查炮点 %d，数据矩阵大小: %d × %d\n', count_s, size(data, 1), size(data, 2));
    if count_s > size(data, 1)
        fprintf('警告: 炮点 %d 超出数据范围，跳过\n', count_s);
        continue;
    end
    
    % 计算激发点位置
    pos_s = nz - 3*pml - (count_s-1) * len_RtoR;
    
    fprintf('\n============ 第%d个炮点信息 ============\n', count_s);
    fprintf('激发点位置（网格坐标）: %d\n', pos_s);
    fprintf('激发点深度（米）: %.2f\n', pos_s * dz);
    
    % 计算实际显示的检波器范围
    receiver_end = min(config.receiver_start + config.num_receivers_to_show - 1, num_receivers);
    actual_num_receivers = receiver_end - config.receiver_start + 1;

    % 创建声波测井标准显示图形
    create_acoustic_logging_display(data, count_s, num_receivers, maxt, ...
                                   display_time, time_indices, config, ...
                                   pos_s, dz, f0, L_RtoR, first_trace_amplitude_factor, ...
                                   len_StoR, len_RtoR);
    
    % 保存高清图形
    fig_filename = fullfile(picture_dir, sprintf('声波测井_炮点_%d.png', count_s));

    % 设置高分辨率保存参数
    set(gcf, 'PaperPositionMode', 'auto');
    set(gcf, 'PaperUnits', 'inches');
    set(gcf, 'PaperSize', [12, 9]);  % 设置纸张大小

    % 使用print函数保存高质量图片
    print(gcf, fig_filename, '-dpng', sprintf('-r%d', config.dpi));
    fprintf('保存高清图片 (%d DPI): %s\n', config.dpi, fig_filename);
    
    % 关闭图形窗口
    close(gcf);
end

fprintf('\n所有图片已保存到: %s\n', picture_dir);
fprintf('程序执行完成！共处理了 %d 个炮点\n', length(shot_numbers));

%% ==================== 辅助函数 ====================
function create_acoustic_logging_display(data, count_s, num_receivers, maxt, ...
                                        display_time, time_indices, config, ...
                                        pos_s, dz, ~, ~, first_trace_amplitude_factor, ...
                                        len_StoR, len_RtoR)
    % 创建声波测井标准显示
    
    % 创建高质量图形窗口
    figure('Position', [100, 100, config.figure_size], 'Color', 'w', ...
           'PaperPositionMode', 'auto');
    hold on;
    
    % 计算归一化因子
    max_amplitude = 0;
    for j = 1:num_receivers
        temp_data = data(count_s, (j-1)*maxt+1:j*maxt);
        max_amplitude = max(max_amplitude, max(abs(temp_data)));
    end
    if max_amplitude < 1e-10
        max_amplitude = 1;
    end
    
    % 计算实际显示的检波器范围
    receiver_start = config.receiver_start;
    receiver_end = min(receiver_start + config.num_receivers_to_show - 1, num_receivers);
    actual_receivers = receiver_end - receiver_start + 1;

    % 绘制每道数据
    first_arrivals = zeros(1, actual_receivers);  % 预分配数组
    first_arrival_count = 0;  % 计数器
    for i = receiver_start:receiver_end
        % 道偏移位置（从1开始连续编号）
        display_index = i - receiver_start + 1;

        % 为模式6的第一道留出更多空间（参考例子图片的空间比例）
        if config.first_trace_original && display_index == 1
            % 第一道：使用更大的间距，为大振幅留出足够空间
            trace_offset = display_index * config.trace_spacing * 4.0;
        elseif config.first_trace_original && display_index > 1
            % 其他道：在第一道的基础上正常间距
            trace_offset = config.trace_spacing * 4.0 + (display_index - 1) * config.trace_spacing;
        else
            % 其他模式：正常间距
            trace_offset = display_index * config.trace_spacing;
        end

        % 提取数据 - 添加边界检查
        data_start_idx = (i-1)*maxt+1;
        data_end_idx = i*maxt;

        % 检查数据索引是否超出范围
        if data_end_idx > size(data, 2)
            error('数据索引超出范围！检波器%d需要索引%d-%d，但数据只有%d列', ...
                  i, data_start_idx, data_end_idx, size(data, 2));
        end

        if count_s > size(data, 1)
            error('炮点编号%d超出数据范围！数据只有%d行', count_s, size(data, 1));
        end

        orig_data = data(count_s, data_start_idx:data_end_idx);

        % 检查时间索引是否超出范围
        if max(time_indices) > length(orig_data)
            warning('时间索引超出范围，使用全部时间数据');
            time_indices = 1:length(orig_data);
            display_time = time_vec(time_indices);
        end

        plot_data = orig_data(time_indices);
        
        % 智能振幅处理
        if config.no_normalization
            % 模式5：不进行归一化，显示原波形
            norm_data = plot_data * config.amplitude_scale;
        elseif config.first_trace_original
            % 模式6：第一道显示原波形，其余道归一化显示
            if i == receiver_start
                % 第一道：显示原波形，但进行适当缩放以避免过大
                % 计算第一道的最大振幅
                first_trace_max = max(abs(plot_data));
                if first_trace_max > 0
                    % 使用可调节的缩放因子，既保留振幅特征又不至于太大
                    % 让第一道的最大振幅为归一化道的指定倍数
                    first_trace_scale = config.amplitude_scale * first_trace_amplitude_factor;
                    norm_data = plot_data / first_trace_max * first_trace_scale;
                else
                    norm_data = plot_data * config.amplitude_scale;
                end
            else
                % 其余道：传统归一化
                norm_data = plot_data / max_amplitude * config.amplitude_scale;
            end
        elseif config.preserve_amplitude
            % 计算该道的最大振幅
            trace_max = max(abs(plot_data));

            if trace_max > 0
                % 混合归一化策略：部分保留振幅差异，部分归一化显示
                % 传统归一化部分
                normalized_part = plot_data / max_amplitude * config.amplitude_scale;
                % 保留振幅部分
                preserved_part = plot_data / trace_max * config.amplitude_scale * (trace_max / max_amplitude);
                % 按比例混合
                norm_data = config.amplitude_balance * preserved_part + ...
                           (1 - config.amplitude_balance) * normalized_part;
            else
                norm_data = plot_data * config.amplitude_scale;
            end
        else
            % 传统归一化
            norm_data = plot_data / max_amplitude * config.amplitude_scale;
        end
        
        % 绘制基线
        plot([display_time(1), display_time(end)], [trace_offset, trace_offset], ...
             'k-', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);
        
        % 绘制波形
        wave_y = norm_data + trace_offset;
        plot(display_time, wave_y, 'k-', 'LineWidth', 1.2);
        
        % 填充正振幅（如果启用）
        if config.fill_positive
            positive_idx = norm_data > 0;
            if any(positive_idx)
                % 找到连续的正振幅区间
                positive_idx = positive_idx(:); % 确保是列向量
                diff_idx = diff([false; positive_idx; false]);
                start_idx = find(diff_idx == 1);
                end_idx = find(diff_idx == -1) - 1;

                % 对每个连续区间分别填充
                for seg = 1:length(start_idx)
                    seg_start = start_idx(seg);
                    seg_end = end_idx(seg);

                    % 提取该区间的数据
                    seg_time = display_time(seg_start:seg_end);
                    seg_wave = wave_y(seg_start:seg_end);
                    seg_baseline = trace_offset * ones(size(seg_time));

                    % 填充该区间
                    fill_x = [seg_time, fliplr(seg_time)];
                    fill_y = [seg_wave, fliplr(seg_baseline)];
                    fill(fill_x, fill_y, config.fill_color, 'EdgeColor', 'none', 'FaceAlpha', 0.6);
                end
            end
        end
        
        % 检测首波到达（如果启用）
        if config.show_first_arrivals
            first_arrival = detect_first_arrival_simple(plot_data, display_time);
            if first_arrival > 0
                plot(first_arrival, trace_offset, 'ro', 'MarkerSize', 6, ...
                     'MarkerFaceColor', 'r', 'MarkerEdgeColor', 'k', 'LineWidth', 1);
                first_arrival_count = first_arrival_count + 1;
                first_arrivals(first_arrival_count) = first_arrival;
            end
        end
        
        % 不在这里添加道标签，因为Y轴已经有标签了
    end
    
    % 设置图形属性
    xlabel('时间 (秒)', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('深度 (米)', 'FontSize', 14, 'FontWeight', 'bold');
    
    title_str = sprintf('第%d炮声波测井数据 (深度: %.2fm)', count_s, pos_s * dz);
    title(title_str, 'FontSize', 16, 'FontWeight', 'bold');
    
    % 网格和轴设置
    grid on;
    set(gca, 'LineWidth', 1.2, 'GridLineStyle', ':', 'GridAlpha', 0.3);
    set(gca, 'FontSize', 11);
    
    % Y轴设置 - 左侧显示物理深度，右侧显示检波器编号
    yticks(config.trace_spacing:config.trace_spacing:actual_receivers*config.trace_spacing);

    % 计算检波器的真实物理深度位置
    depth_labels = cell(1, actual_receivers);

    for i = 1:actual_receivers
        actual_receiver_num = receiver_start + i - 1;

        % 使用正确的检波器位置计算公式
        % 检波器在震源上方，使用实际的几何参数
        receiver_grid_pos = pos_s - len_StoR - (actual_receiver_num-1) * len_RtoR;
        receiver_depth = receiver_grid_pos * dz;

        depth_labels{i} = sprintf('%.2f', receiver_depth);
    end

    % 设置左侧Y轴标签（深度）
    yticklabels(depth_labels);

    % 范围设置
    xlim([display_time(1), display_time(end)]);

    % 为模式6的第一道调整Y轴范围，确保大振幅波形完全显示
    if config.first_trace_original
        % 第一道需要更多底部空间来显示负振幅，增加到5倍空间
        y_bottom = -5.0 * config.trace_spacing;  % 为第一道的负振幅留出更多空间
        y_top = (actual_receivers+0.5)*config.trace_spacing;
        ylim([y_bottom, y_top]);
    else
        % 其他模式使用原来的范围
        ylim([0.5*config.trace_spacing, (actual_receivers+0.5)*config.trace_spacing]);
    end
    
    % 不添加左上角信息框
end

function first_arrival_time = detect_first_arrival_simple(data, time_vec)
    % 简单的首波到达检测
    abs_data = abs(data);
    threshold = 0.05 * max(abs_data);
    first_idx = find(abs_data > threshold, 1, 'first');
    
    if ~isempty(first_idx) && first_idx > 1
        first_arrival_time = time_vec(first_idx);
    else
        first_arrival_time = -1;
    end
end
