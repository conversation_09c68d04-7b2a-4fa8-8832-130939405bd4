%==========================================================================
%                    参数调整示例脚本
%==========================================================================
% 功能说明：演示如何调整检波器和DAS系统参数
% 使用方法：修改此脚本中的参数，然后将参数复制到best_main.m中
%==========================================================================

clc; clear;

fprintf('=== 检波器与DAS系统参数调整示例 ===\n');

%--------------------------------------------------------------------------
%                    1. 检波器参数调整
%--------------------------------------------------------------------------

% 检波器数量选项
N_options = [11, 15, 21, 25, 31];  % 常用的检波器数量
fprintf('\n1. 检波器数量选项:\n');
for i = 1:length(N_options)
    fprintf('   选项%d: %d个检波器\n', i, N_options(i));
end

% 推荐设置
N_recommended = 21;  % 推荐21个检波器
fprintf('   推荐设置: %d个检波器\n', N_recommended);

%--------------------------------------------------------------------------
%                    2. DAS系统参数调整
%--------------------------------------------------------------------------

fprintf('\n2. DAS系统参数调整:\n');

% 标距长度选项 (米)
gauge_length_options = [0.4, 0.6, 0.8, 1.0];
fprintf('   标距长度选项:\n');
for i = 1:length(gauge_length_options)
    fprintf('     %.1f米 - ', gauge_length_options(i));
    if gauge_length_options(i) <= 0.4
        fprintf('较短标距，高频响应好\n');
    elseif gauge_length_options(i) <= 0.6
        fprintf('平衡设置，推荐使用\n');
    elseif gauge_length_options(i) <= 0.8
        fprintf('较长标距，低频响应好\n');
    else
        fprintf('长标距，信噪比高但分辨率降低\n');
    end
end

% 标距重合比例选项
gauge_overlap_options = [0.5, 0.6, 0.7, 0.8];
fprintf('   标距重合比例选项:\n');
for i = 1:length(gauge_overlap_options)
    fprintf('     %.1f%% - ', gauge_overlap_options(i)*100);
    if gauge_overlap_options(i) <= 0.5
        fprintf('低重合，独立性好\n');
    elseif gauge_overlap_options(i) <= 0.6
        fprintf('中等重合，平衡设置\n');
    elseif gauge_overlap_options(i) <= 0.7
        fprintf('高重合，推荐使用\n');
    else
        fprintf('很高重合，连续性最好\n');
    end
end

%--------------------------------------------------------------------------
%                    3. 推荐参数组合
%--------------------------------------------------------------------------

fprintf('\n3. 推荐参数组合:\n');

% 组合1：标准配置
fprintf('   组合1 - 标准配置（推荐）:\n');
fprintf('     检波器数量: %d\n', 21);
fprintf('     DAS标距点数量: %d\n', 21);
fprintf('     标距长度: %.1f米\n', 0.6);
fprintf('     标距重合比例: %.1f%%\n', 70);
fprintf('     适用场景: 常规声波测井，平衡性能\n');

% 组合2：高分辨率配置
fprintf('   组合2 - 高分辨率配置:\n');
fprintf('     检波器数量: %d\n', 31);
fprintf('     DAS标距点数量: %d\n', 31);
fprintf('     标距长度: %.1f米\n', 0.4);
fprintf('     标距重合比例: %.1f%%\n', 60);
fprintf('     适用场景: 需要高空间分辨率的精细测量\n');

% 组合3：高信噪比配置
fprintf('   组合3 - 高信噪比配置:\n');
fprintf('     检波器数量: %d\n', 15);
fprintf('     DAS标距点数量: %d\n', 15);
fprintf('     标距长度: %.1f米\n', 0.8);
fprintf('     标距重合比例: %.1f%%\n', 80);
fprintf('     适用场景: 噪声环境较强的测量\n');

%--------------------------------------------------------------------------
%                    4. 参数修改指南
%--------------------------------------------------------------------------

fprintf('\n4. 参数修改指南:\n');
fprintf('   在best_main.m中找到以下代码段（约第44-53行）:\n');
fprintf('   \n');
fprintf('   %% 1.1.4 检波器与DAS系统参数（集中配置）\n');
fprintf('   N = 21;                    %% 检波器数量\n');
fprintf('   num_das_points = N;        %% DAS标距点数量\n');
fprintf('   gauge_length = 0.6;        %% 标距长度(米)\n');
fprintf('   gauge_overlap = 0.7;       %% 标距重合比例\n');
fprintf('   enable_das = true;         %% DAS系统开关\n');
fprintf('   \n');
fprintf('   根据需要修改上述参数值。\n');

%--------------------------------------------------------------------------
%                    5. 参数影响分析
%--------------------------------------------------------------------------

fprintf('\n5. 参数影响分析:\n');
fprintf('   检波器数量影响:\n');
fprintf('     - 增加数量: 提高空间采样密度，增加数据量\n');
fprintf('     - 减少数量: 降低计算量，但可能损失空间分辨率\n');
fprintf('   \n');
fprintf('   DAS标距长度影响:\n');
fprintf('     - 增加长度: 提高信噪比，但降低空间分辨率\n');
fprintf('     - 减少长度: 提高空间分辨率，但可能降低信噪比\n');
fprintf('   \n');
fprintf('   DAS重合比例影响:\n');
fprintf('     - 增加重合: 提高数据连续性和稳定性\n');
fprintf('     - 减少重合: 提高数据独立性，但可能有间隙\n');

%--------------------------------------------------------------------------
%                    6. 测试建议
%--------------------------------------------------------------------------

fprintf('\n6. 测试建议:\n');
fprintf('   1. 首次运行使用推荐的标准配置\n');
fprintf('   2. 根据结果调整参数，重新运行\n');
fprintf('   3. 对比不同参数设置的结果\n');
fprintf('   4. 选择最适合具体应用的参数组合\n');

fprintf('\n=== 参数调整示例完成 ===\n');

%--------------------------------------------------------------------------
%                    7. 快速参数设置函数
%--------------------------------------------------------------------------

function set_standard_config()
    % 标准配置
    fprintf('\n应用标准配置:\n');
    fprintf('N = 21;\n');
    fprintf('num_das_points = N;\n');
    fprintf('gauge_length = 0.6;\n');
    fprintf('gauge_overlap = 0.7;\n');
    fprintf('enable_das = true;\n');
end

function set_high_resolution_config()
    % 高分辨率配置
    fprintf('\n应用高分辨率配置:\n');
    fprintf('N = 31;\n');
    fprintf('num_das_points = N;\n');
    fprintf('gauge_length = 0.4;\n');
    fprintf('gauge_overlap = 0.6;\n');
    fprintf('enable_das = true;\n');
end

function set_high_snr_config()
    % 高信噪比配置
    fprintf('\n应用高信噪比配置:\n');
    fprintf('N = 15;\n');
    fprintf('num_das_points = N;\n');
    fprintf('gauge_length = 0.8;\n');
    fprintf('gauge_overlap = 0.8;\n');
    fprintf('enable_das = true;\n');
end
