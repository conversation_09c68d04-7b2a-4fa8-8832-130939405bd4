%==========================================================================
%                    DAS集成测试脚本
%==========================================================================
% 功能说明：测试修改后的best_main.m程序中DAS系统的集成情况
% 验证DAS和检波器数据的同时采集和存储功能
%==========================================================================

clc; clear; close all;

fprintf('=== DAS集成测试开始 ===\n');

% 运行修改后的主程序（只运行前2炮进行测试）
fprintf('正在运行修改后的best_main.m程序...\n');

try
    % 运行主程序
    run('best_main.m');
    
    fprintf('\n=== 测试结果验证 ===\n');
    
    % 验证数据结构
    fprintf('1. 数据结构验证:\n');
    fprintf('   检波器数据矩阵大小: %d × %d\n', size(data, 1), size(data, 2));
    fprintf('   DAS数据矩阵大小: %d × %d\n', size(das_data, 1), size(das_data, 2));
    fprintf('   检波器道数: %d\n', N);
    fprintf('   DAS标距点数: %d\n', num_das_points);
    
    % 验证数据内容
    fprintf('\n2. 数据内容验证:\n');
    detector_max = max(abs(data(:)));
    das_max = max(abs(das_data(:)));
    fprintf('   检波器数据最大值: %.6e\n', detector_max);
    fprintf('   DAS数据最大值: %.6e\n', das_max);
    
    % 验证DAS参数
    fprintf('\n3. DAS系统参数验证:\n');
    fprintf('   标距长度: %.2f米\n', gauge_length);
    fprintf('   标距重合比例: %.1f%%\n', gauge_overlap*100);
    fprintf('   标距中心点位置范围: %.2f - %.2f (网格点)\n', ...
            min(gauge_centers), max(gauge_centers));
    
    % 创建简单的数据对比图
    fprintf('\n4. 生成数据对比图:\n');
    figure('Position', [100, 100, 1000, 600]);
    
    % 选择第一炮的第一道数据进行对比
    if size(data, 1) >= 1 && size(das_data, 1) >= 1
        % 提取第一炮数据
        detector_trace = data(1, 1:maxt);  % 第一炮第一道检波器数据
        das_trace = das_data(1, 1:maxt);   % 第一炮第一个标距点DAS数据
        
        time_ms = (1:maxt) * dt * 1000;    % 时间轴（毫秒）
        
        subplot(2,1,1);
        plot(time_ms, detector_trace, 'b-', 'LineWidth', 1.5);
        title('第1炮第1道检波器数据');
        xlabel('时间 (ms)');
        ylabel('幅值');
        grid on;
        
        subplot(2,1,2);
        plot(time_ms, das_trace, 'r-', 'LineWidth', 1.5);
        title('第1炮第1个标距点DAS应变率数据');
        xlabel('时间 (ms)');
        ylabel('应变率');
        grid on;
        
        % 保存测试图
        test_fig_name = 'DAS_Integration_Test_Result.png';
        saveas(gcf, test_fig_name);
        fprintf('   测试结果图已保存为: %s\n', test_fig_name);
    end
    
    fprintf('\n=== 测试成功完成 ===\n');
    fprintf('DAS系统已成功集成到声波测井正演模拟程序中！\n');
    fprintf('主要特点:\n');
    fprintf('- DAS与检波器同时部署在相同位置\n');
    fprintf('- 标距长度优化为%.2f米，重叠率%.1f%%\n', gauge_length, gauge_overlap*100);
    fprintf('- 同时保存检波器和DAS数据，便于后续对比分析\n');
    fprintf('- 数据文件包含完整的系统参数信息\n');
    
catch ME
    fprintf('\n=== 测试过程中出现错误 ===\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    fprintf('请检查程序代码并修复错误。\n');
end

fprintf('\n=== DAS集成测试结束 ===\n');
