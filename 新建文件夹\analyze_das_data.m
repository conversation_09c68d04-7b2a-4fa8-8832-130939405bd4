%==========================================================================
%                    DAS数据分析脚本
%==========================================================================
% 功能说明：分析DAS和检波器数据，生成对比图表
% 使用方法：先运行best_main.m生成数据，然后运行此脚本进行分析
%==========================================================================

function analyze_das_data(data_filename)
    % 如果没有提供文件名，尝试找到最新的数据文件
    if nargin < 1
        files = dir('FDTD_SeismicLogging_*.mat');
        if isempty(files)
            error('未找到数据文件，请先运行best_main.m生成数据');
        end
        % 选择最新的文件
        [~, idx] = max([files.datenum]);
        data_filename = files(idx).name;
        fprintf('使用数据文件: %s\n', data_filename);
    end
    
    % 加载数据
    fprintf('正在加载数据文件: %s\n', data_filename);
    load(data_filename);
    
    % 验证数据完整性
    if ~exist('das_data', 'var')
        error('数据文件中缺少DAS数据，请使用修改后的程序重新生成数据');
    end
    
    fprintf('数据加载完成！\n');
    fprintf('检波器数据: %d炮 × %d道 × %d时间点\n', size(data,1), N, maxt);
    fprintf('DAS数据: %d炮 × %d标距点 × %d时间点\n', size(das_data,1), num_das_points, maxt);
    
    %======================================================================
    %                        1. 单炮数据对比分析
    %======================================================================
    
    fprintf('\n正在生成单炮数据对比图...\n');
    
    % 选择中间炮进行分析
    shot_to_analyze = ceil(size(data,1)/2);
    
    % 重构单炮数据
    detector_shot = reshape(data(shot_to_analyze, :), [N, maxt]);
    das_shot = reshape(das_data(shot_to_analyze, :), [num_das_points, maxt]);
    
    % 时间轴
    time_ms = (1:maxt) * dt * 1000;  % 毫秒
    
    % 创建单炮对比图
    figure('Position', [100, 100, 1400, 1000]);
    
    % 子图1：检波器数据波形图
    subplot(2,3,1);
    imagesc(time_ms, 1:N, detector_shot);
    colorbar;
    title(['第', num2str(shot_to_analyze), '炮检波器数据']);
    xlabel('时间 (ms)');
    ylabel('检波器道数');
    
    % 子图2：DAS数据波形图
    subplot(2,3,2);
    imagesc(time_ms, 1:num_das_points, das_shot);
    colorbar;
    title(['第', num2str(shot_to_analyze), '炮DAS应变率数据']);
    xlabel('时间 (ms)');
    ylabel('DAS标距点');
    
    % 子图3：中间道对比
    mid_channel = ceil(N/2);
    subplot(2,3,3);
    plot(time_ms, detector_shot(mid_channel, :), 'b-', 'LineWidth', 1.5);
    hold on;
    plot(time_ms, das_shot(mid_channel, :), 'r-', 'LineWidth', 1.5);
    legend('检波器', 'DAS', 'Location', 'best');
    title(['第', num2str(mid_channel), '道/标距点对比']);
    xlabel('时间 (ms)');
    ylabel('归一化幅值');
    grid on;
    
    % 子图4：多道叠加显示（检波器）
    subplot(2,3,4);
    hold on;
    for i = 1:min(5, N)  % 显示前5道
        normalized_trace = detector_shot(i, :) / max(abs(detector_shot(i, :)));
        plot(time_ms, normalized_trace + i*2, 'b-', 'LineWidth', 1);
    end
    title('检波器多道显示');
    xlabel('时间 (ms)');
    ylabel('道数 + 偏移');
    grid on;
    
    % 子图5：多道叠加显示（DAS）
    subplot(2,3,5);
    hold on;
    for i = 1:min(5, num_das_points)  % 显示前5个标距点
        normalized_trace = das_shot(i, :) / max(abs(das_shot(i, :)));
        plot(time_ms, normalized_trace + i*2, 'r-', 'LineWidth', 1);
    end
    title('DAS多标距点显示');
    xlabel('时间 (ms)');
    ylabel('标距点 + 偏移');
    grid on;
    
    % 子图6：频谱对比
    subplot(2,3,6);
    % 计算频谱
    fs = 1/dt;  % 采样频率
    freq = (0:maxt-1) * fs / maxt;
    
    detector_fft = abs(fft(detector_shot(mid_channel, :)));
    das_fft = abs(fft(das_shot(mid_channel, :)));
    
    plot(freq(1:maxt/2), detector_fft(1:maxt/2), 'b-', 'LineWidth', 1.5);
    hold on;
    plot(freq(1:maxt/2), das_fft(1:maxt/2), 'r-', 'LineWidth', 1.5);
    legend('检波器', 'DAS', 'Location', 'best');
    title('频谱对比');
    xlabel('频率 (Hz)');
    ylabel('幅值');
    grid on;
    xlim([0, f0*3]);  % 显示到3倍主频
    
    % 保存单炮对比图
    single_shot_fig = ['Single_Shot_Comparison_', datestr(now, 'yyyymmdd_HHMMSS'), '.png'];
    saveas(gcf, single_shot_fig);
    fprintf('单炮对比图已保存: %s\n', single_shot_fig);
    
    %======================================================================
    %                        2. 多炮数据汇总分析
    %======================================================================
    
    fprintf('正在生成多炮数据汇总图...\n');
    
    % 创建多炮汇总图
    figure('Position', [200, 200, 1200, 800]);
    
    % 子图1：所有炮检波器数据
    subplot(2,2,1);
    imagesc(1:size(data,2), 1:size(data,1), data);
    colorbar;
    title('所有炮检波器数据汇总');
    xlabel('数据点 (道×时间)');
    ylabel('炮号');
    
    % 子图2：所有炮DAS数据
    subplot(2,2,2);
    imagesc(1:size(das_data,2), 1:size(das_data,1), das_data);
    colorbar;
    title('所有炮DAS数据汇总');
    xlabel('数据点 (标距点×时间)');
    ylabel('炮号');
    
    % 子图3：数据统计对比
    subplot(2,2,3);
    detector_stats = [mean(abs(data(:))), std(abs(data(:))), max(abs(data(:)))];
    das_stats = [mean(abs(das_data(:))), std(abs(das_data(:))), max(abs(das_data(:)))];
    
    bar_data = [detector_stats; das_stats]';
    bar(bar_data);
    set(gca, 'XTickLabel', {'均值', '标准差', '最大值'});
    legend('检波器', 'DAS', 'Location', 'best');
    title('数据统计对比');
    ylabel('幅值');
    
    % 子图4：信噪比分析
    subplot(2,2,4);
    % 简单的信噪比估计（前10%作为噪声，后90%作为信号+噪声）
    noise_samples = round(maxt * 0.1);
    
    detector_snr = zeros(size(data,1), 1);
    das_snr = zeros(size(das_data,1), 1);
    
    for i = 1:size(data,1)
        % 检波器SNR
        det_trace = data(i, 1:maxt);  % 第一道
        noise_power = mean(det_trace(1:noise_samples).^2);
        signal_power = mean(det_trace(noise_samples+1:end).^2);
        detector_snr(i) = 10*log10(signal_power/noise_power);
        
        % DAS SNR
        das_trace = das_data(i, 1:maxt);  % 第一个标距点
        noise_power = mean(das_trace(1:noise_samples).^2);
        signal_power = mean(das_trace(noise_samples+1:end).^2);
        das_snr(i) = 10*log10(signal_power/noise_power);
    end
    
    plot(1:length(detector_snr), detector_snr, 'b-o', 'LineWidth', 1.5);
    hold on;
    plot(1:length(das_snr), das_snr, 'r-s', 'LineWidth', 1.5);
    legend('检波器', 'DAS', 'Location', 'best');
    title('信噪比对比');
    xlabel('炮号');
    ylabel('SNR (dB)');
    grid on;
    
    % 保存多炮汇总图
    multi_shot_fig = ['Multi_Shot_Summary_', datestr(now, 'yyyymmdd_HHMMSS'), '.png'];
    saveas(gcf, multi_shot_fig);
    fprintf('多炮汇总图已保存: %s\n', multi_shot_fig);
    
    %======================================================================
    %                        3. 生成分析报告
    %======================================================================
    
    fprintf('\n=== DAS与检波器数据分析报告 ===\n');
    fprintf('数据文件: %s\n', data_filename);
    fprintf('分析时间: %s\n', datestr(now));
    fprintf('\n系统配置:\n');
    fprintf('- 检波器数量: %d\n', N);
    fprintf('- DAS标距点数量: %d\n', num_das_points);
    fprintf('- 标距长度: %.2f米\n', gauge_length);
    fprintf('- 标距重合比例: %.1f%%\n', gauge_overlap*100);
    fprintf('- 时间采样点数: %d\n', maxt);
    fprintf('- 时间步长: %.2e秒\n', dt);
    
    fprintf('\n数据统计:\n');
    fprintf('- 检波器数据范围: %.2e ~ %.2e\n', min(data(:)), max(data(:)));
    fprintf('- DAS数据范围: %.2e ~ %.2e\n', min(das_data(:)), max(das_data(:)));
    fprintf('- 检波器平均SNR: %.2f dB\n', mean(detector_snr));
    fprintf('- DAS平均SNR: %.2f dB\n', mean(das_snr));
    
    fprintf('\n结论:\n');
    fprintf('- DAS系统成功集成，与检波器同位置部署\n');
    fprintf('- 数据质量良好，具有可比性\n');
    fprintf('- 建议进一步优化标距参数以提高信噪比\n');
    
    fprintf('\n分析完成！\n');
end

% 如果直接运行此脚本，调用主函数
if ~exist('data_filename', 'var')
    analyze_das_data();
end
