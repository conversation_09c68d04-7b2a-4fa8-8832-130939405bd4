clc;clear;
%二维声波测井数值模拟  
%二维弹性波方程,二阶时间，四阶空间中心差分，规则网格
%规则网格和交错网格的差分格式及系数均不同,对于pml边界，设置好左右上下外边界即可,不能有未赋值的位置，否则产生反射。
%代码优化，尽量取消循环
%---------------------------------设置参数---------------------------------
% |--vp2--||-vp1-||-vp2-|-vp3-|
%井孔
po1=1.0*10^3;%kg/m^3
vp1=1500;
vs1=0;
%地层一（靠近井眼）
po2=2.3*10^3;
vp2=4500;
vs2=2300;
% %地层二（远离井眼）
% po3=2.4*10^3;
% vp3=4500;
% vs3=2650;
% %主频
f0=10*10^3;%Hz
%井径
cal=0.1;%m
%计算每个波长包含多少网格v=la*f,la=v/f,最小波长用最小v，v=1500
la1=vp1/f0;%0.15一个波长
%空间间隔、时间间隔,满足稳定性的最小时间间隔
vmax=4500;%最大速度
n1=9/8;%9/8--交错网格系数
n2=-1/24;%--交错网格系数
dx=la1/10;%0.015
dz=la1/10;%0.015
dt=dx/(1.7321*vmax*(abs(n1)+abs(n2)));%  2.3093e-006
%仪器信息及模型大小len_z*len_x
num_s=67;%要模拟的炮数--60
% L_StoR=3;%源距m200个dx,400个dx/2
L_StoR=1.5;
L_RtoR=0.15;%接收间距m,10个dx,20个dx/2
N=8;%DAS标距点数量 - 修改为8个
len_StoR=fix(L_StoR/dz);%源距包含的网格数,200
len_RtoR=fix(L_RtoR/dz);%接收间距包含的网格数,10
%仪器长度=0.3+48*0.15=7.5m，60炮*0.15=9m,总长度9+7.5=16.5m,长度设为16.5(1100个dx),宽度设为9m，
%（600个dx）
%仪器长度包含270个网格，
pml=50;%50*0.015=0.75m
nx=2*pml+200;%500+2*50=600
nz=2*pml+1200;%1000+2*50=1100,Elapsed time is 801.671988 seconds.

%%%%%%%%%%%设置震源时间参数
maxt=2000;%1.4us:5000=7000us=7ms,时间点数
% 设置波场显示间隔
frame_interval = 10; % 每10个时间步显示一次波场
%划分网格

% ===== DAS系统参数设置 =====
% 使用DAS作为唯一的检波方式，用应变率的变化代替检波器的相位信息
num_gauge_points = N;  % 与原始程序中的接收点数量(N)保持一致，通常为6

% DAS标距设置
gauge_length = 0.4;     % 标距长度(米)
gauge_overlap = 0.5;    % 标距重合比例(0-1之间) - 对于8个标距点保持不变

% 使用与原程序相同的接收点位置布局
% 原始程序中的接收点间距为len_RtoR (0.15m)
% 第一个接收点距离震源位置为len_StoR (1.5m)

% 初始化DAS数据存储数组 - 应变率
das_strain_rate = zeros(num_gauge_points, maxt);  % 每个标距点随时间的应变率
gauge_centers = zeros(num_gauge_points, 1);       % 每个标距中心点的位置（距离）

% 使用原程序中的接收点位置定义标距中心点
% 在原程序中，接收点位置是：pos_s-len_StoR:-len_RtoR:pos_s-len_StoR-(N-1)*len_RtoR
for i = 1:num_gauge_points
    % 每个标距的中心位置对应原始程序中的检波点位置
    gauge_centers(i) = len_StoR + (i-1) * len_RtoR;
end

fprintf('DAS覆盖范围: %.2f米 - %.2f米, 共%d个标距点\n', gauge_centers(1), gauge_centers(end), num_gauge_points);
% 初始化速度和应力变量（更紧凑的初始化方式）
[Vx, Vx_1, Vx_3] = deal(zeros(nz,nx));
[Vz, Vz_1, Vz_3] = deal(zeros(nz,nx));
[Tao_xx, Tao_xx_1, Tao_xx_3] = deal(zeros(nz,nx));
[Tao_zz, Tao_zz_1, Tao_zz_3] = deal(zeros(nz,nx));
[Tao_xz, Tao_xz_1, Tao_xz_3] = deal(zeros(nz,nx));
%%%%%%%%%%%设置速度和密度参数
% vp=ones(nz,nx).*2500;
% vs=ones(nz,nx).*1200;
% dens=ones(nz,nx).*2000;
vp=zeros(nz,nx);
vs=zeros(nz,nx);
dens=zeros(nz,nx);
med_x=fix(nx/2)-fix(nx/4);%井轴放在左边一半的一半位置
l_cal=ceil(cal/dx)/1;%井径8*2*dx=16*0.015=0.24m

Formation_D=1.0;   %井旁侵入带厚度
Formation_DIter=ceil(Formation_D/dz);  %一个地层所占用的网格数
Formation_H=1.0;   %井旁侵入带高度
% Formation_Flag=2;  %井旁地层个数
Formation_HIter=ceil(Formation_H/dz);  %一个地层所占用的网格数
% Vpin=3000;Vpout=5000;Vsin=1732;Vsout=2880;Denin=2000;Denout=2200;  %侵入带参数设定

for count_j=1:1:nx
    if(count_j<med_x-l_cal)    %地层一，靠近井眼
        vp(:,count_j)=vp2;
        vs(:,count_j)=vs2;
        dens(:,count_j)=po2;
    elseif(count_j>=med_x-l_cal&count_j<=med_x+l_cal)    %井眼
        vp(:,count_j)=vp1;
        vs(:,count_j)=vs1;
        dens(:,count_j)=po1;
    elseif(count_j>med_x+l_cal)    %地层一，靠近井眼
        vp(:,count_j)=vp2;
        vs(:,count_j)=vs2;
        dens(:,count_j)=po2;
    end
%      elseif(count_j>med_x+fix(med_x/2)&count_j<=nx)    %地层二，远离井眼
%        vp(:,count_j)=vp3;
%         vs(:,count_j)=vs3;
%         dens(:,count_j)=po3;
%     end
end
%射孔
Vpout=2300;Vsout=1300;Denout=1800;
% 地层的设定
 for count_i=nz/2-ceil(Formation_HIter/2):nz/2+ceil(Formation_HIter/2)
     for count_j=med_x+l_cal+1:1:med_x+l_cal+Formation_DIter
         vp(count_i,count_j)=Vpout;
         vs(count_i,count_j)=Vsout;
         dens(count_i,count_j)=Denout;
     end
 end

 % %阶梯
 % Nor=med_x+l_cal+1;   
 % for H=1:Formation_Flag
 %     Nor1=Nor+(H-1)*Formation_Iter;
 %     Nor2=Nor+H*Formation_Iter;
 %     vp(:,Nor1:Nor2)=Vpin+(Vpout-Vpin)/(Formation_Flag-1)*(H-1);
 %     vs(:,Nor1:Nor2)=Vpin+(Vsout-Vsin)/(Formation_Flag-1)*(H-1);
 %     dens(:,Nor1:Nor2)=Denin+(Denout-Denin)/(Formation_Flag-1)*(H-1);
 % end

% %线性
% Nor=med_x+l_cal+1;
% Nor3=Nor+Formation_Iter;
% for j=Nor:1:Nor3
%     vp(:,j)=Vpin+(Vpout-Vpin)/(Nor3-Nor)*(j-Nor);
%     vs(:,j)=Vsin+(Vsout-Vsin)/(Nor3-Nor)*(j-Nor);
%     dens(:,j)=Denin+(Denout-Denin)/(Nor3-Nor)*(j-Nor);
% end


%地层设定结束
%调试1
% figure(3001);imagesc(vp);figure(3002);imagesc(dens);figure(3003);imagesc(vs); % 注释掉不需要的图形显示
%

%% 
% % figure(3002);imagesc(dens);figure(3003);imagesc(vs);      
            
%求miu
%% 使用矩阵运算计算弹性参数（替代嵌套循环，提高效率）
% 计算切变弹性模量
miu = dens .* (vs.^2);

% 计算Lame参数
lmd = dens .* (vp.^2) - 2 * miu;
% %%%%%%对密度进行平均
% %x方向
% for count_i=1:1:nz
%     for count_j=fix(nx/2)+1:1:nx-1
%         dens(count_i,count_j)=(dens(count_i,count_j-1)+dens(count_i,count_j+1))/2;
%     end
% end
% %z方向
% for count_i=1+1:1:nz-1
%     for count_j=fix(nx/2):1:nx
%         dens(count_i,count_j)=(dens(count_i-1,count_j)+dens(count_i+1,count_j))/2;
%     end
% end
% %%%%%%对miu进行调和平均
% %x方向
% for count_i=1:1:nz
%     for count_j=1+1:1:nx-1
%         miu(count_i,count_j)=2/(1/(miu(count_i,count_j-1)+eps)+1/(miu(count_i,count_j+1)+eps));
%     end
% end
% %z方向
% for count_i=1+1:1:nz-1
%     for count_j=1:1:nx
%         miu(count_i,count_j)=2/(1/(miu(count_i-1,count_j)+eps)+1/(miu(count_i+1,count_j)+eps));
%     end
% end
%%%%%%%%%%%%%%%%%求取固定参数
%% 使用矩阵运算计算p参数（替代循环，提高效率）
% 初始化p参数
[p0, p1, p2, p3] = deal(zeros(nz,nx));

% 内部计算（排除边界）
for count_j=2:1:nx-1
    p0(:,count_j) = 2*dt./(dens(:,count_j+1) + dens(:,count_j-1)); % 密度平均
end

% 直接使用矩阵运算计算p1, p2, p3
p1 = dt * (lmd + 2*miu);
p2 = dt * lmd;
p3 = dt * miu;

% 处理边界条件
p0(:,1) = dt./dens(:,1);
p0(:,nx) = dt./dens(:,nx);
% a0=10^6;%???
% da=2*pi*a0*f0;
%%%%%%%%%%%吸收边界参数
M=5;
R=1.0e-6;
da=log(1/R)*1.5*vmax;%6*vmax
%%%%%%%%%%%设置震源
% 初始化波形数据存储数组
data = zeros(num_s, num_gauge_points*maxt);
x = zeros(num_s, N*maxt);
f=zeros(1,maxt);
kexi=f0^2/0.1512;
ts=1.5/f0;%时移参数,1.5000e-004
t=(0:maxt-1).*dt;
for count_i=1:1:maxt
    T=t(count_i)-ts;
    f(count_i)=2*kexi*(1-2*kexi*T^2)*exp(-kexi*T^2);
end
%%%%%%%%%%初始化变量
count_i=0;
count_j=0;
tep=0;
tep1=0;
tep2=0;
tep3=0;

kz=0;
w3=0;
az=zeros(nz,nx);%az=0;
bz=zeros(nz,nx);%bz=0;

kx=0;
w1=0;
ax=zeros(nz,nx);%ax=0;
bx=zeros(nz,nx);%bx=0;
%%%%%%取数据用,60道
data=zeros(num_s,N*maxt);%所有DAS数据，N道一行
X=zeros(N,maxt);%一炮的N道DAS数据
% 
% for t=1:maxt
%     f(t)=(1-2*(pi*f0*((t-1)*dt-ts))^2)*exp(-(pi*f0*((t-1)*dt-ts))^2);
% end
%  plot(t,f); axis([t(1) t(maxt) -14*10^8 7*10^8]);
tic
%打开动画文件,10帧
%mov = avifile('tanxingbo5_qingxie2.avi','fps',10);
count_F=1;%
%-----------------------------开始计算-------------------------------------
%炮数循环，共num_s=60炮
for count_s=1:1:num_s
    pos_s=nz-3*pml-(count_s-1)*len_RtoR;%每一炮的震源在z轴位置
    %每次开始都要归零
    for count_i=1:1:nz
        for count_j=1:1:nx
            Vx(count_i,count_j)=0;
            Vx_1(count_i,count_j)=0;
            Vx_3(count_i,count_j)=0;
            Vz(count_i,count_j)=0;
            Vz_1(count_i,count_j)=0;
            Vz_3(count_i,count_j)=0;
            Tao_xx(count_i,count_j)=0;
            Tao_xx_1(count_i,count_j)=0;
            Tao_xx_3(count_i,count_j)=0;
            Tao_zz(count_i,count_j)=0;
            Tao_zz_1(count_i,count_j)=0;
            Tao_zz_3(count_i,count_j)=0;
            Tao_xz(count_i,count_j)=0;
            Tao_xz_1(count_i,count_j)=0;
            Tao_xz_3(count_i,count_j)=0;
        end
    end
    
%时间循环,前面有个负号，所以第一式是减号-
for count_t=1:1:maxt
   
    %---------------------------------更新应力参数--------------------------
    %计算内部区域Txx,Tzz,Txz
    %加载震源
            Tao_xx(pos_s,med_x)=Tao_xx(pos_s,med_x)+f(count_t);%加载源
            Tao_zz(pos_s,med_x)=Tao_zz(pos_s,med_x)+f(count_t);
    Tao_xx(pml+1:nz-pml,pml+1:nx-pml)=Tao_xx(pml+1:nz-pml,pml+1:nx-pml)+(p1(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vx(pml+1:nz-pml,(pml+1:nx-pml)+1)-Vx(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(pml+1:nx-pml)+2)-Vx(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx)+p2(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vz((pml+1:nz-pml)+1,pml+1:nx-pml)-Vz((pml+1:nz-pml)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,pml+1:nx-pml)-Vz((pml+1:nz-pml)-2,pml+1:nx-pml))/dz));
    Tao_zz(pml+1:nz-pml,pml+1:nx-pml)=Tao_zz(pml+1:nz-pml,pml+1:nx-pml)+(p2(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vx(pml+1:nz-pml,(pml+1:nx-pml)+1)-Vx(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(pml+1:nx-pml)+2)-Vx(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx)+p1(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vz((pml+1:nz-pml)+1,pml+1:nx-pml)-Vz((pml+1:nz-pml)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,pml+1:nx-pml)-Vz((pml+1:nz-pml)-2,pml+1:nx-pml))/dz));  
    Tao_xz(pml+1:nz-pml,pml+1:nx-pml)=Tao_xz(pml+1:nz-pml,pml+1:nx-pml)+p3(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vx((pml+1:nz-pml)+1,pml+1:nx-pml)-Vx((pml+1:nz-pml)-1,pml+1:nx-pml))/dz+...
                n2*(Vx((pml+1:nz-pml)+2,pml+1:nx-pml)-Vx((pml+1:nz-pml)-2,pml+1:nx-pml))/dz+n1*(Vz(pml+1:nz-pml,(pml+1:nx-pml)+1)-Vz(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Vz(pml+1:nz-pml,(pml+1:nx-pml)+2)-Vz(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx);
            
%     for count_i=pml+1:nz-pml
%         for count_j=pml+1:nx-pml
%             tep1=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
%             tep2=dt*lmd(count_i,count_j);
%             tep3=dt*miu(count_i,count_j);
%             Tao_xx(count_i,count_j)=Tao_xx(count_i,count_j)-(tep1*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx)+tep2*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_zz(count_i,count_j)=Tao_zz(count_i,count_j)-(tep2*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx)+tep1*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));  
%             Tao_xz(count_i,count_j)=Tao_xz(count_i,count_j)-tep3*(n1*(Vx(count_i+1,count_j)-Vx(count_i-1,count_j))/dz+...
%                 n2*(Vx(count_i+2,count_j)-Vx(count_i-2,count_j))/dz+n1*(Vz(count_i,count_j+1)-Vz(count_i,count_j-1))/dx+...
%                 n2*(Vz(count_i,count_j+2)-Vz(count_i,count_j-2))/dx);
%             %震源位置
%             if(count_i==pos_s&count_j==med_x)  %if(count_i==fix(nz/2)&count_j==fix(nx/2))
%                 Tao_xx(count_i,count_j)=Tao_xx(count_i,count_j)+f(count_t);%加载源
%                 Tao_zz(count_i,count_j)=Tao_zz(count_i,count_j)+f(count_t);
%             end
%         end
%     end 
    %%%计算上边界T  
    for count_i=1+2:pml
        kz=(pml-count_i)/pml;
        w3=da*kz^M;
        az(count_i,:)=1/(1+0.5*w3*dt);%每一行都一样
        bz(count_i,:)=1-0.5*w3*dt;
    end
     %---------左上角,w1!=0,w3!=0
         for count_j=1+2:pml
             kx=(pml-count_j)/pml;
             w1=da*kx^M;
             ax(:,count_j)=1/(1+0.5*w1*dt);%每一列都一样
             bx(:,count_j)=1-0.5*w1*dt;
         end
            %Tao_xx
            Tao_xx_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Tao_xx_1(3:pml,3:pml)+p1(3:pml,3:pml).*(n1*(Vx(3:pml,(3:pml)+1)-Vx(3:pml,(3:pml)-1))/dx+...
                n2*(Vx(3:pml,(3:pml)+2)-Vx(3:pml,(3:pml)-2))/dx));
            Tao_xx_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Tao_xx_3(3:pml,3:pml)+p2(3:pml,3:pml).*(n1*(Vz((3:pml)+1,3:pml)-Vz((3:pml)-1,3:pml))/dz+...
                n2*(Vz((3:pml)+2,3:pml)-Vz((3:pml)-2,3:pml))/dz));
            Tao_xx(3:pml,3:pml)=Tao_xx_1(3:pml,3:pml)+Tao_xx_3(3:pml,3:pml);
            %Tao_zz
            Tao_zz_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Tao_zz_1(3:pml,3:pml)+p2(3:pml,3:pml).*(n1*(Vx(3:pml,(3:pml)+1)-Vx(3:pml,(3:pml)-1))/dx+...
                n2*(Vx(3:pml,(3:pml)+2)-Vx(3:pml,(3:pml)-2))/dx));
            Tao_zz_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Tao_zz_3(3:pml,3:pml)+p1(3:pml,3:pml).*(n1*(Vz((3:pml)+1,3:pml)-Vz((3:pml)-1,3:pml))/dz+...
                n2*(Vz((3:pml)+2,3:pml)-Vz((3:pml)-2,3:pml))/dz));
            Tao_zz(3:pml,3:pml)=Tao_zz_1(3:pml,3:pml)+Tao_zz_3(3:pml,3:pml);
            %Tao_xz
            Tao_xz_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Tao_xz_1(3:pml,3:pml)+p3(3:pml,3:pml).*(n1*(Vz(3:pml,(3:pml)+1)-Vz(3:pml,(3:pml)-1))/dx+...
                n2*(Vz(3:pml,(3:pml)+2)-Vz(3:pml,(3:pml)-2))/dx));
            Tao_xz_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Tao_xz_3(3:pml,3:pml)+p3(3:pml,3:pml).*(n1*(Vx((3:pml)+1,3:pml)-Vx((3:pml)-1,3:pml))/dz+...
                n2*(Vx((3:pml)+2,3:pml)-Vx((3:pml)-2,3:pml))/dz));
            Tao_xz(3:pml,3:pml)=Tao_xz_1(3:pml,3:pml)+Tao_xz_3(3:pml,3:pml);
     %--------中上,w1=0,w3!=0
             %Tao_xx
            Tao_xx_1(3:pml,pml+1:nx-pml)=Tao_xx_1(3:pml,pml+1:nx-pml)+p1(3:pml,pml+1:nx-pml).*(n1*(Vx(3:pml,(pml+1:nx-pml)+1)-Vx(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(3:pml,(pml+1:nx-pml)+2)-Vx(3:pml,(pml+1:nx-pml)-2))/dx);
            Tao_xx_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Tao_xx_3(3:pml,pml+1:nx-pml)+p2(3:pml,pml+1:nx-pml).*(n1*(Vz((3:pml)+1,pml+1:nx-pml)-Vz((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((3:pml)+2,pml+1:nx-pml)-Vz((3:pml)-2,pml+1:nx-pml))/dz));
            Tao_xx(3:pml,pml+1:nx-pml)=Tao_xx_1(3:pml,pml+1:nx-pml)+Tao_xx_3(3:pml,pml+1:nx-pml);
            %Tao_zz
            Tao_zz_1(3:pml,pml+1:nx-pml)=Tao_zz_1(3:pml,pml+1:nx-pml)+p2(3:pml,pml+1:nx-pml).*(n1*(Vx(3:pml,(pml+1:nx-pml)+1)-Vx(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(3:pml,(pml+1:nx-pml)+2)-Vx(3:pml,(pml+1:nx-pml)-2))/dx);
            Tao_zz_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Tao_zz_3(3:pml,pml+1:nx-pml)+p1(3:pml,pml+1:nx-pml).*(n1*(Vz((3:pml)+1,pml+1:nx-pml)-Vz((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((3:pml)+2,pml+1:nx-pml)-Vz((3:pml)-2,pml+1:nx-pml))/dz));
            Tao_zz(3:pml,pml+1:nx-pml)=Tao_zz_1(3:pml,pml+1:nx-pml)+Tao_zz_3(3:pml,pml+1:nx-pml);
            %Tao_xz
            Tao_xz_1(3:pml,pml+1:nx-pml)=Tao_xz_1(3:pml,pml+1:nx-pml)+p3(3:pml,pml+1:nx-pml).*(n1*(Vz(3:pml,(pml+1:nx-pml)+1)-Vz(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Vz(3:pml,(pml+1:nx-pml)+2)-Vz(3:pml,(pml+1:nx-pml)-2))/dx);
            Tao_xz_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Tao_xz_3(3:pml,pml+1:nx-pml)+p3(3:pml,pml+1:nx-pml).*(n1*(Vx((3:pml)+1,pml+1:nx-pml)-Vx((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Vx((3:pml)+2,pml+1:nx-pml)-Vx((3:pml)-2,pml+1:nx-pml))/dz));
            Tao_xz(3:pml,pml+1:nx-pml)=Tao_xz_1(3:pml,pml+1:nx-pml)+Tao_xz_3(3:pml,pml+1:nx-pml);
    %--------右上角，w1!=0,w3!=0
         for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
         end
            %Tao_xx
            Tao_xx_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Tao_xx_1(3:pml,nx-pml+1:nx-2)+p1(3:pml,nx-pml+1:nx-2).*(n1*(Vx(3:pml,(nx-pml+1:nx-2)+1)-Vx(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(3:pml,(nx-pml+1:nx-2)+2)-Vx(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_xx_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Tao_xx_3(3:pml,nx-pml+1:nx-2)+p2(3:pml,nx-pml+1:nx-2).*(n1*(Vz((3:pml)+1,nx-pml+1:nx-2)-Vz((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((3:pml)+2,nx-pml+1:nx-2)-Vz((3:pml)-2,nx-pml+1:nx-2))/dz));
            Tao_xx(3:pml,nx-pml+1:nx-2)=Tao_xx_1(3:pml,nx-pml+1:nx-2)+Tao_xx_3(3:pml,nx-pml+1:nx-2);
            %Tao_zz
            Tao_zz_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Tao_zz_1(3:pml,nx-pml+1:nx-2)+p2(3:pml,nx-pml+1:nx-2).*(n1*(Vx(3:pml,(nx-pml+1:nx-2)+1)-Vx(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(3:pml,(nx-pml+1:nx-2)+2)-Vx(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_zz_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Tao_zz_3(3:pml,nx-pml+1:nx-2)+p1(3:pml,nx-pml+1:nx-2).*(n1*(Vz((3:pml)+1,nx-pml+1:nx-2)-Vz((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((3:pml)+2,nx-pml+1:nx-2)-Vz((3:pml)-2,nx-pml+1:nx-2))/dz));
            Tao_zz(3:pml,nx-pml+1:nx-2)=Tao_zz_1(3:pml,nx-pml+1:nx-2)+Tao_zz_3(3:pml,nx-pml+1:nx-2);
            %Tao_xz
            Tao_xz_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Tao_xz_1(3:pml,nx-pml+1:nx-2)+p3(3:pml,nx-pml+1:nx-2).*(n1*(Vz(3:pml,(nx-pml+1:nx-2)+1)-Vz(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vz(3:pml,(nx-pml+1:nx-2)+2)-Vz(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_xz_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Tao_xz_3(3:pml,nx-pml+1:nx-2)+p3(3:pml,nx-pml+1:nx-2).*(n1*(Vx((3:pml)+1,nx-pml+1:nx-2)-Vx((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vx((3:pml)+2,nx-pml+1:nx-2)-Vx((3:pml)-2,nx-pml+1:nx-2))/dz));
            Tao_xz(3:pml,nx-pml+1:nx-2)=Tao_xz_1(3:pml,nx-pml+1:nx-2)+Tao_xz_3(3:pml,nx-pml+1:nx-2);       

%     %计算上边界T
%     for count_i=1+2:pml
%         kz=(pml-count_i)/pml;
%         w3=da*kz^M;
%         az=1/(1+0.5*w3*dt);
%         bz=1-0.5*w3*dt;
%         %左上角,w1!=0,w3!=0
%         for count_j=1+2:pml
%             kx=(pml-count_j)/pml;
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             %lam=dens(count_i,count_j)*v(count_i,count_j)^2*dt;
%             tep1=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
%             tep2=dt*lmd(count_i,count_j);
%             tep3=dt*miu(count_i,count_j);
%             %Tao_xx
%             Tao_xx_1(count_i,count_j)=ax*(bx*Tao_xx_1(count_i,count_j)-tep1*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_xx_3(count_i,count_j)=az*(bz*Tao_xx_3(count_i,count_j)-tep2*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_xx(count_i,count_j)=Tao_xx_1(count_i,count_j)+Tao_xx_3(count_i,count_j);
%             %Tao_zz
%             Tao_zz_1(count_i,count_j)=ax*(bx*Tao_zz_1(count_i,count_j)-tep2*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_zz_3(count_i,count_j)=az*(bz*Tao_zz_3(count_i,count_j)-tep1*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_zz(count_i,count_j)=Tao_zz_1(count_i,count_j)+Tao_zz_3(count_i,count_j);
%             %Tao_xz
%             Tao_xz_1(count_i,count_j)=ax*(bx*Tao_xz_1(count_i,count_j)-tep3*(n1*(Vz(count_i,count_j+1)-Vz(count_i,count_j-1))/dx+...
%                 n2*(Vz(count_i,count_j+2)-Vz(count_i,count_j-2))/dx));
%             Tao_xz_3(count_i,count_j)=az*(bz*Tao_xz_3(count_i,count_j)-tep3*(n1*(Vx(count_i+1,count_j)-Vx(count_i-1,count_j))/dz+...
%                 n2*(Vx(count_i+2,count_j)-Vx(count_i-2,count_j))/dz));
%             Tao_xz(count_i,count_j)=Tao_xz_1(count_i,count_j)+Tao_xz_3(count_i,count_j);
%         end
%         %中上,w1=0,w3!=0
%         for count_j=pml+1:nx-pml
%             %lam=dens(count_i,count_j)*v(count_i,count_j)^2*dt;
%             tep1=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
%             tep2=dt*lmd(count_i,count_j);
%             tep3=dt*miu(count_i,count_j);
%             %Tao_xx
%             Tao_xx_1(count_i,count_j)=Tao_xx_1(count_i,count_j)-tep1*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx);
%             Tao_xx_3(count_i,count_j)=az*(bz*Tao_xx_3(count_i,count_j)-tep2*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_xx(count_i,count_j)=Tao_xx_1(count_i,count_j)+Tao_xx_3(count_i,count_j);
%             %Tao_zz
%             Tao_zz_1(count_i,count_j)=Tao_zz_1(count_i,count_j)-tep2*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx);
%             Tao_zz_3(count_i,count_j)=az*(bz*Tao_zz_3(count_i,count_j)-tep1*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_zz(count_i,count_j)=Tao_zz_1(count_i,count_j)+Tao_zz_3(count_i,count_j);
%             %Tao_xz
%             Tao_xz_1(count_i,count_j)=Tao_xz_1(count_i,count_j)-tep3*(n1*(Vz(count_i,count_j+1)-Vz(count_i,count_j-1))/dx+...
%                 n2*(Vz(count_i,count_j+2)-Vz(count_i,count_j-2))/dx);
%             Tao_xz_3(count_i,count_j)=az*(bz*Tao_xz_3(count_i,count_j)-tep3*(n1*(Vx(count_i+1,count_j)-Vx(count_i-1,count_j))/dz+...
%                 n2*(Vx(count_i+2,count_j)-Vx(count_i-2,count_j))/dz));
%             Tao_xz(count_i,count_j)=Tao_xz_1(count_i,count_j)+Tao_xz_3(count_i,count_j);
%             
%         end
%         %右上角，w1!=0,w3!=0
%         for count_j=nx-pml+1:nx-2
%             kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             %lam=dens(count_i,count_j)*v(count_i,count_j)^2*dt;
%             tep1=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
%             tep2=dt*lmd(count_i,count_j);
%             tep3=dt*miu(count_i,count_j);
%             %Tao_xx
%             Tao_xx_1(count_i,count_j)=ax*(bx*Tao_xx_1(count_i,count_j)-tep1*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_xx_3(count_i,count_j)=az*(bz*Tao_xx_3(count_i,count_j)-tep2*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_xx(count_i,count_j)=Tao_xx_1(count_i,count_j)+Tao_xx_3(count_i,count_j);
%             %Tao_zz
%             Tao_zz_1(count_i,count_j)=ax*(bx*Tao_zz_1(count_i,count_j)-tep2*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_zz_3(count_i,count_j)=az*(bz*Tao_zz_3(count_i,count_j)-tep1*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_zz(count_i,count_j)=Tao_zz_1(count_i,count_j)+Tao_zz_3(count_i,count_j);
%             %Tao_xz
%             Tao_xz_1(count_i,count_j)=ax*(bx*Tao_xz_1(count_i,count_j)-tep3*(n1*(Vz(count_i,count_j+1)-Vz(count_i,count_j-1))/dx+...
%                 n2*(Vz(count_i,count_j+2)-Vz(count_i,count_j-2))/dx));
%             Tao_xz_3(count_i,count_j)=az*(bz*Tao_xz_3(count_i,count_j)-tep3*(n1*(Vx(count_i+1,count_j)-Vx(count_i-1,count_j))/dz+...
%                 n2*(Vx(count_i+2,count_j)-Vx(count_i-2,count_j))/dz));
%             Tao_xz(count_i,count_j)=Tao_xz_1(count_i,count_j)+Tao_xz_3(count_i,count_j);       
%         end
%     end    
    %%%%%%%%计算左右边界T,w1!=0,w3=0
        %左边界
        for count_j=1+2:pml
            kx=(pml-count_j)/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
        %Tao_xx
            Tao_xx_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Tao_xx_1(pml+1:nz-pml,3:pml)+p1(pml+1:nz-pml,3:pml).*(n1*(Vx(pml+1:nz-pml,(3:pml)+1)-Vx(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(3:pml)+2)-Vx(pml+1:nz-pml,(3:pml)-2))/dx));
            Tao_xx_3(pml+1:nz-pml,3:pml)=Tao_xx_3(pml+1:nz-pml,3:pml)+p2(pml+1:nz-pml,3:pml).*(n1*(Vz((pml+1:nz-pml)+1,3:pml)-Vz((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,3:pml)-Vz((pml+1:nz-pml)-2,3:pml))/dz);
            Tao_xx(pml+1:nz-pml,3:pml)=Tao_xx_1(pml+1:nz-pml,3:pml)+Tao_xx_3(pml+1:nz-pml,3:pml);
            %Tao_zz
            Tao_zz_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Tao_zz_1(pml+1:nz-pml,3:pml)+p2(pml+1:nz-pml,3:pml).*(n1*(Vx(pml+1:nz-pml,(3:pml)+1)-Vx(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(3:pml)+2)-Vx(pml+1:nz-pml,(3:pml)-2))/dx));
            Tao_zz_3(pml+1:nz-pml,3:pml)=Tao_zz_3(pml+1:nz-pml,3:pml)+p1(pml+1:nz-pml,3:pml).*(n1*(Vz((pml+1:nz-pml)+1,3:pml)-Vz((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,3:pml)-Vz((pml+1:nz-pml)-2,3:pml))/dz);
            Tao_zz(pml+1:nz-pml,3:pml)=Tao_zz_1(pml+1:nz-pml,3:pml)+Tao_zz_3(pml+1:nz-pml,3:pml);
            %Tao_xz
            Tao_xz_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Tao_xz_1(pml+1:nz-pml,3:pml)+p3(pml+1:nz-pml,3:pml).*(n1*(Vz(pml+1:nz-pml,(3:pml)+1)-Vz(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Vz(pml+1:nz-pml,(3:pml)+2)-Vz(pml+1:nz-pml,(3:pml)-2))/dx));
            Tao_xz_3(pml+1:nz-pml,3:pml)=Tao_xz_3(pml+1:nz-pml,3:pml)+p3(pml+1:nz-pml,3:pml).*(n1*(Vx((pml+1:nz-pml)+1,3:pml)-Vx((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Vx((pml+1:nz-pml)+2,3:pml)-Vx((pml+1:nz-pml)-2,3:pml))/dz);
            Tao_xz(pml+1:nz-pml,3:pml)=Tao_xz_1(pml+1:nz-pml,3:pml)+Tao_xz_3(pml+1:nz-pml,3:pml);       
         %右边界
        for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
             %Tao_xx
            Tao_xx_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Tao_xx_1(pml+1:nz-pml,nx-pml+1:nx-2)+p1(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vx(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Vx(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Vx(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_xx_3(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_xx_3(pml+1:nz-pml,nx-pml+1:nx-2)+p2(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vz((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Vz((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Vz((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);
            Tao_xx(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_xx_1(pml+1:nz-pml,nx-pml+1:nx-2)+Tao_xx_3(pml+1:nz-pml,nx-pml+1:nx-2);
            %Tao_zz
            Tao_zz_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Tao_zz_1(pml+1:nz-pml,nx-pml+1:nx-2)+p2(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vx(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Vx(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Vx(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_zz_3(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_zz_3(pml+1:nz-pml,nx-pml+1:nx-2)+p1(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vz((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Vz((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Vz((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);
            Tao_zz(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_zz_1(pml+1:nz-pml,nx-pml+1:nx-2)+Tao_zz_3(pml+1:nz-pml,nx-pml+1:nx-2);
            %Tao_xz
            Tao_xz_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Tao_xz_1(pml+1:nz-pml,nx-pml+1:nx-2)+p3(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vz(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Vz(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vz(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Vz(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_xz_3(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_xz_3(pml+1:nz-pml,nx-pml+1:nx-2)+p3(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vx((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Vx((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vx((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Vx((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);
            Tao_xz(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_xz_1(pml+1:nz-pml,nx-pml+1:nx-2)+Tao_xz_3(pml+1:nz-pml,nx-pml+1:nx-2);    
    
    
%     %计算左右边界T,w1!=0,w3=0
%     for count_i=pml+1:nz-pml
%         %左边界
%         for count_j=1+2:pml
%             kx=(pml-count_j)/pml;
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             %lam=dens(count_i,count_j)*v(count_i,count_j)^2*dt;
%             tep1=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
%             tep2=dt*lmd(count_i,count_j);
%             tep3=dt*miu(count_i,count_j);
%             %Tao_xx
%             Tao_xx_1(count_i,count_j)=ax*(bx*Tao_xx_1(count_i,count_j)-tep1*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_xx_3(count_i,count_j)=Tao_xx_3(count_i,count_j)-tep2*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz);
%             Tao_xx(count_i,count_j)=Tao_xx_1(count_i,count_j)+Tao_xx_3(count_i,count_j);
%             %Tao_zz
%             Tao_zz_1(count_i,count_j)=ax*(bx*Tao_zz_1(count_i,count_j)-tep2*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_zz_3(count_i,count_j)=Tao_zz_3(count_i,count_j)-tep1*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz);
%             Tao_zz(count_i,count_j)=Tao_zz_1(count_i,count_j)+Tao_zz_3(count_i,count_j);
%             %Tao_xz
%             Tao_xz_1(count_i,count_j)=ax*(bx*Tao_xz_1(count_i,count_j)-tep3*(n1*(Vz(count_i,count_j+1)-Vz(count_i,count_j-1))/dx+...
%                 n2*(Vz(count_i,count_j+2)-Vz(count_i,count_j-2))/dx));
%             Tao_xz_3(count_i,count_j)=Tao_xz_3(count_i,count_j)-tep3*(n1*(Vx(count_i+1,count_j)-Vx(count_i-1,count_j))/dz+...
%                 n2*(Vx(count_i+2,count_j)-Vx(count_i-2,count_j))/dz);
%             Tao_xz(count_i,count_j)=Tao_xz_1(count_i,count_j)+Tao_xz_3(count_i,count_j);       
%         end
%         %右边界
%         for count_j=nx-pml+1:nx-2
%             kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             %lam=dens(count_i,count_j)*v(count_i,count_j)^2*dt;
%             tep1=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
%             tep2=dt*lmd(count_i,count_j);
%             tep3=dt*miu(count_i,count_j);
%             %Tao_xx
%             Tao_xx_1(count_i,count_j)=ax*(bx*Tao_xx_1(count_i,count_j)-tep1*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_xx_3(count_i,count_j)=Tao_xx_3(count_i,count_j)-tep2*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz);
%             Tao_xx(count_i,count_j)=Tao_xx_1(count_i,count_j)+Tao_xx_3(count_i,count_j);
%             %Tao_zz
%             Tao_zz_1(count_i,count_j)=ax*(bx*Tao_zz_1(count_i,count_j)-tep2*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_zz_3(count_i,count_j)=Tao_zz_3(count_i,count_j)-tep1*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz);
%             Tao_zz(count_i,count_j)=Tao_zz_1(count_i,count_j)+Tao_zz_3(count_i,count_j);
%             %Tao_xz
%             Tao_xz_1(count_i,count_j)=ax*(bx*Tao_xz_1(count_i,count_j)-tep3*(n1*(Vz(count_i,count_j+1)-Vz(count_i,count_j-1))/dx+...
%                 n2*(Vz(count_i,count_j+2)-Vz(count_i,count_j-2))/dx));
%             Tao_xz_3(count_i,count_j)=Tao_xz_3(count_i,count_j)-tep3*(n1*(Vx(count_i+1,count_j)-Vx(count_i-1,count_j))/dz+...
%                 n2*(Vx(count_i+2,count_j)-Vx(count_i-2,count_j))/dz);
%             Tao_xz(count_i,count_j)=Tao_xz_1(count_i,count_j)+Tao_xz_3(count_i,count_j);    
%         end
%     end  
    %%%%%%%%计算下边界T
    for count_i=nz-pml+1:nz-2
        kz=(count_i-(nz-pml+1))/pml;
        w3=da*kz^M;
        az(count_i,:)=1/(1+0.5*w3*dt);
        bz(count_i,:)=1-0.5*w3*dt;
    end
    %左下角,w1!=0,w3!=0
        for count_j=1+2:pml
            kx=(pml-count_j)/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            %Tao_xx
            Tao_xx_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Tao_xx_1(nz-pml+1:nz-2,3:pml)+p1(nz-pml+1:nz-2,3:pml).*(n1*(Vx(nz-pml+1:nz-2,(3:pml)+1)-Vx(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(3:pml)+2)-Vx(nz-pml+1:nz-2,(3:pml)-2))/dx));
            Tao_xx_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Tao_xx_3(nz-pml+1:nz-2,3:pml)+p2(nz-pml+1:nz-2,3:pml).*(n1*(Vz((nz-pml+1:nz-2)+1,3:pml)-Vz((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,3:pml)-Vz((nz-pml+1:nz-2)-2,3:pml))/dz));
            Tao_xx(nz-pml+1:nz-2,3:pml)=Tao_xx_1(nz-pml+1:nz-2,3:pml)+Tao_xx_3(nz-pml+1:nz-2,3:pml);
            %Tao_zz
            Tao_zz_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Tao_zz_1(nz-pml+1:nz-2,3:pml)+p2(nz-pml+1:nz-2,3:pml).*(n1*(Vx(nz-pml+1:nz-2,(3:pml)+1)-Vx(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(3:pml)+2)-Vx(nz-pml+1:nz-2,(3:pml)-2))/dx));
            Tao_zz_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Tao_zz_3(nz-pml+1:nz-2,3:pml)+p1(nz-pml+1:nz-2,3:pml).*(n1*(Vz((nz-pml+1:nz-2)+1,3:pml)-Vz((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,3:pml)-Vz((nz-pml+1:nz-2)-2,3:pml))/dz));
            Tao_zz(nz-pml+1:nz-2,3:pml)=Tao_zz_1(nz-pml+1:nz-2,3:pml)+Tao_zz_3(nz-pml+1:nz-2,3:pml);
            %Tao_xz
            Tao_xz_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Tao_xz_1(nz-pml+1:nz-2,3:pml)+p3(nz-pml+1:nz-2,3:pml).*(n1*(Vz(nz-pml+1:nz-2,(3:pml)+1)-Vz(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Vz(nz-pml+1:nz-2,(3:pml)+2)-Vz(nz-pml+1:nz-2,(3:pml)-2))/dx));
            Tao_xz_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Tao_xz_3(nz-pml+1:nz-2,3:pml)+p3(nz-pml+1:nz-2,3:pml).*(n1*(Vx((nz-pml+1:nz-2)+1,3:pml)-Vx((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Vx((nz-pml+1:nz-2)+2,3:pml)-Vx((nz-pml+1:nz-2)-2,3:pml))/dz));
            Tao_xz(nz-pml+1:nz-2,3:pml)=Tao_xz_1(nz-pml+1:nz-2,3:pml)+Tao_xz_3(nz-pml+1:nz-2,3:pml); 
    %中下,w1=0,w3!=0
            %Tao_xx
            Tao_xx_1(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_xx_1(nz-pml+1:nz-2,pml+1:nx-pml)+p1(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vx(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Vx(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Vx(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
            Tao_xx_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Tao_xx_3(nz-pml+1:nz-2,pml+1:nx-pml)+p2(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vz((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Vz((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Vz((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
            Tao_xx(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_xx_1(nz-pml+1:nz-2,pml+1:nx-pml)+Tao_xx_3(nz-pml+1:nz-2,pml+1:nx-pml);
            %Tao_zz
            Tao_zz_1(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_zz_1(nz-pml+1:nz-2,pml+1:nx-pml)+p2(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vx(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Vx(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Vx(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
            Tao_zz_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Tao_zz_3(nz-pml+1:nz-2,pml+1:nx-pml)+p1(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vz((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Vz((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Vz((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
            Tao_zz(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_zz_1(nz-pml+1:nz-2,pml+1:nx-pml)+Tao_zz_3(nz-pml+1:nz-2,pml+1:nx-pml);
            %Tao_xz
            Tao_xz_1(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_xz_1(nz-pml+1:nz-2,pml+1:nx-pml)+p3(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vz(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Vz(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Vz(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Vz(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
            Tao_xz_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Tao_xz_3(nz-pml+1:nz-2,pml+1:nx-pml)+p3(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vx((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Vx((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Vx((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Vx((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
            Tao_xz(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_xz_1(nz-pml+1:nz-2,pml+1:nx-pml)+Tao_xz_3(nz-pml+1:nz-2,pml+1:nx-pml);     
    %右下角，w1!=0,w3!=0
        for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            %Tao_xx
            Tao_xx_1(nz-pml+1:nz-2,nx-pml+1:nx-2)=ax(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bx(nz-pml+1:nz-2,nx-pml+1:nx-2).*Tao_xx_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+p1(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Vx(nz-pml+1:nz-2,(nx-pml+1:nx-2)+1)-Vx(nz-pml+1:nz-2,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(nx-pml+1:nx-2)+2)-Vx(nz-pml+1:nz-2,(nx-pml+1:nx-2)-2))/dx));
            Tao_xx_3(nz-pml+1:nz-2,nx-pml+1:nx-2)=az(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bz(nz-pml+1:nz-2,nx-pml+1:nx-2).*Tao_xx_3(nz-pml+1:nz-2,nx-pml+1:nx-2)+p2(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Vz((nz-pml+1:nz-2)+1,nx-pml+1:nx-2)-Vz((nz-pml+1:nz-2)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,nx-pml+1:nx-2)-Vz((nz-pml+1:nz-2)-2,nx-pml+1:nx-2))/dz));
            Tao_xx(nz-pml+1:nz-2,nx-pml+1:nx-2)=Tao_xx_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+Tao_xx_3(nz-pml+1:nz-2,nx-pml+1:nx-2);
            %Tao_zz
            Tao_zz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)=ax(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bx(nz-pml+1:nz-2,nx-pml+1:nx-2).*Tao_zz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+p2(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Vx(nz-pml+1:nz-2,(nx-pml+1:nx-2)+1)-Vx(nz-pml+1:nz-2,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(nx-pml+1:nx-2)+2)-Vx(nz-pml+1:nz-2,(nx-pml+1:nx-2)-2))/dx));
            Tao_zz_3(nz-pml+1:nz-2,nx-pml+1:nx-2)=az(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bz(nz-pml+1:nz-2,nx-pml+1:nx-2).*Tao_zz_3(nz-pml+1:nz-2,nx-pml+1:nx-2)+p1(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Vz((nz-pml+1:nz-2)+1,nx-pml+1:nx-2)-Vz((nz-pml+1:nz-2)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,nx-pml+1:nx-2)-Vz((nz-pml+1:nz-2)-2,nx-pml+1:nx-2))/dz));
            Tao_zz(nz-pml+1:nz-2,nx-pml+1:nx-2)=Tao_zz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+Tao_zz_3(nz-pml+1:nz-2,nx-pml+1:nx-2);
            %Tao_xz
            Tao_xz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)=ax(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bx(nz-pml+1:nz-2,nx-pml+1:nx-2).*Tao_xz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+p3(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Vz(nz-pml+1:nz-2,(nx-pml+1:nx-2)+1)-Vz(nz-pml+1:nz-2,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vz(nz-pml+1:nz-2,(nx-pml+1:nx-2)+2)-Vz(nz-pml+1:nz-2,(nx-pml+1:nx-2)-2))/dx));
            Tao_xz_3(nz-pml+1:nz-2,nx-pml+1:nx-2)=az(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bz(nz-pml+1:nz-2,nx-pml+1:nx-2).*Tao_xz_3(nz-pml+1:nz-2,nx-pml+1:nx-2)+p3(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Vx((nz-pml+1:nz-2)+1,nx-pml+1:nx-2)-Vx((nz-pml+1:nz-2)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vx((nz-pml+1:nz-2)+2,nx-pml+1:nx-2)-Vx((nz-pml+1:nz-2)-2,nx-pml+1:nx-2))/dz));
            Tao_xz(nz-pml+1:nz-2,nx-pml+1:nx-2)=Tao_xz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+Tao_xz_3(nz-pml+1:nz-2,nx-pml+1:nx-2);     
            
  
%     %计算下边界T
%     for count_i=nz-pml+1:nz-2
%         kz=(count_i-(nz-pml+1))/pml;
%         w3=da*kz^M;
%         az=1/(1+0.5*w3*dt);
%         bz=1-0.5*w3*dt;
%         %左下角,w1!=0,w3!=0
%         for count_j=1+2:pml
%             kx=(pml-count_j)/pml;
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             %lam=dens(count_i,count_j)*v(count_i,count_j)^2*dt;
%             tep1=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
%             tep2=dt*lmd(count_i,count_j);
%             tep3=dt*miu(count_i,count_j);
%             %Tao_xx
%             Tao_xx_1(count_i,count_j)=ax*(bx*Tao_xx_1(count_i,count_j)-tep1*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_xx_3(count_i,count_j)=az*(bz*Tao_xx_3(count_i,count_j)-tep2*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_xx(count_i,count_j)=Tao_xx_1(count_i,count_j)+Tao_xx_3(count_i,count_j);
%             %Tao_zz
%             Tao_zz_1(count_i,count_j)=ax*(bx*Tao_zz_1(count_i,count_j)-tep2*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_zz_3(count_i,count_j)=az*(bz*Tao_zz_3(count_i,count_j)-tep1*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_zz(count_i,count_j)=Tao_zz_1(count_i,count_j)+Tao_zz_3(count_i,count_j);
%             %Tao_xz
%             Tao_xz_1(count_i,count_j)=ax*(bx*Tao_xz_1(count_i,count_j)-tep3*(n1*(Vz(count_i,count_j+1)-Vz(count_i,count_j-1))/dx+...
%                 n2*(Vz(count_i,count_j+2)-Vz(count_i,count_j-2))/dx));
%             Tao_xz_3(count_i,count_j)=az*(bz*Tao_xz_3(count_i,count_j)-tep3*(n1*(Vx(count_i+1,count_j)-Vx(count_i-1,count_j))/dz+...
%                 n2*(Vx(count_i+2,count_j)-Vx(count_i-2,count_j))/dz));
%             Tao_xz(count_i,count_j)=Tao_xz_1(count_i,count_j)+Tao_xz_3(count_i,count_j);     
%         end
%         %中下,w1=0,w3!=0
%         for count_j=pml+1:nx-pml
%             %lam=dens(count_i,count_j)*v(count_i,count_j)^2*dt;
%             tep1=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
%             tep2=dt*lmd(count_i,count_j);
%             tep3=dt*miu(count_i,count_j);
%             %Tao_xx
%             Tao_xx_1(count_i,count_j)=Tao_xx_1(count_i,count_j)-tep1*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx);
%             Tao_xx_3(count_i,count_j)=az*(bz*Tao_xx_3(count_i,count_j)-tep2*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_xx(count_i,count_j)=Tao_xx_1(count_i,count_j)+Tao_xx_3(count_i,count_j);
%             %Tao_zz
%             Tao_zz_1(count_i,count_j)=Tao_zz_1(count_i,count_j)-tep2*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx);
%             Tao_zz_3(count_i,count_j)=az*(bz*Tao_zz_3(count_i,count_j)-tep1*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_zz(count_i,count_j)=Tao_zz_1(count_i,count_j)+Tao_zz_3(count_i,count_j);
%             %Tao_xz
%             Tao_xz_1(count_i,count_j)=Tao_xz_1(count_i,count_j)-tep3*(n1*(Vz(count_i,count_j+1)-Vz(count_i,count_j-1))/dx+...
%                 n2*(Vz(count_i,count_j+2)-Vz(count_i,count_j-2))/dx);
%             Tao_xz_3(count_i,count_j)=az*(bz*Tao_xz_3(count_i,count_j)-tep3*(n1*(Vx(count_i+1,count_j)-Vx(count_i-1,count_j))/dz+...
%                 n2*(Vx(count_i+2,count_j)-Vx(count_i-2,count_j))/dz));
%             Tao_xz(count_i,count_j)=Tao_xz_1(count_i,count_j)+Tao_xz_3(count_i,count_j);     
%         end
%         %右下角，w1!=0,w3!=0
%         for count_j=nx-pml+1:nx-2
%             kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             %lam=dens(count_i,count_j)*v(count_i,count_j)^2*dt;
%             tep1=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
%             tep2=dt*lmd(count_i,count_j);
%             tep3=dt*miu(count_i,count_j);
%             %Tao_xx
%             Tao_xx_1(count_i,count_j)=ax*(bx*Tao_xx_1(count_i,count_j)-tep1*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_xx_3(count_i,count_j)=az*(bz*Tao_xx_3(count_i,count_j)-tep2*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_xx(count_i,count_j)=Tao_xx_1(count_i,count_j)+Tao_xx_3(count_i,count_j);
%             %Tao_zz
%             Tao_zz_1(count_i,count_j)=ax*(bx*Tao_zz_1(count_i,count_j)-tep2*(n1*(Vx(count_i,count_j+1)-Vx(count_i,count_j-1))/dx+...
%                 n2*(Vx(count_i,count_j+2)-Vx(count_i,count_j-2))/dx));
%             Tao_zz_3(count_i,count_j)=az*(bz*Tao_zz_3(count_i,count_j)-tep1*(n1*(Vz(count_i+1,count_j)-Vz(count_i-1,count_j))/dz+...
%                 n2*(Vz(count_i+2,count_j)-Vz(count_i-2,count_j))/dz));
%             Tao_zz(count_i,count_j)=Tao_zz_1(count_i,count_j)+Tao_zz_3(count_i,count_j);
%             %Tao_xz
%             Tao_xz_1(count_i,count_j)=ax*(bx*Tao_xz_1(count_i,count_j)-tep3*(n1*(Vz(count_i,count_j+1)-Vz(count_i,count_j-1))/dx+...
%                 n2*(Vz(count_i,count_j+2)-Vz(count_i,count_j-2))/dx));
%             Tao_xz_3(count_i,count_j)=az*(bz*Tao_xz_3(count_i,count_j)-tep3*(n1*(Vx(count_i+1,count_j)-Vx(count_i-1,count_j))/dz+...
%                 n2*(Vx(count_i+2,count_j)-Vx(count_i-2,count_j))/dz));
%             Tao_xz(count_i,count_j)=Tao_xz_1(count_i,count_j)+Tao_xz_3(count_i,count_j);     
%         end
%     end  
    %---------------------------------更新速度参数--------------------------
    %计算内部区域速度
    %Vx
    Vx(pml+1:nz-pml,pml+1:nx-pml)=Vx(pml+1:nz-pml,pml+1:nx-pml)+p0(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Tao_xx(pml+1:nz-pml,(pml+1:nx-pml)+1)-Tao_xx(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xx(pml+1:nz-pml,(pml+1:nx-pml)+2)-Tao_xx(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx+n1*(Tao_xz((pml+1:nz-pml)+1,pml+1:nx-pml)-Tao_xz((pml+1:nz-pml)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_xz((pml+1:nz-pml)+2,pml+1:nx-pml)-Tao_xz((pml+1:nz-pml)-2,pml+1:nx-pml))/dz);
    %Vz        
    Vz(pml+1:nz-pml,pml+1:nx-pml)=Vz(pml+1:nz-pml,pml+1:nx-pml)+p0(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Tao_xz(pml+1:nz-pml,(pml+1:nx-pml)+1)-Tao_xz(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xz(pml+1:nz-pml,(pml+1:nx-pml)+2)-Tao_xz(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx+n1*(Tao_zz((pml+1:nz-pml)+1,pml+1:nx-pml)-Tao_zz((pml+1:nz-pml)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_zz((pml+1:nz-pml)+2,pml+1:nx-pml)-Tao_zz((pml+1:nz-pml)-2,pml+1:nx-pml))/dz);
    
%     for count_i=pml+1:nz-pml  
%         for count_j=pml+1:nx-pml  %+1--对应最小count_j-1，-2--对应最大count_j+2
%             tep=dt/dens(count_i,count_j);
%             Vx(count_i,count_j)=Vx(count_i,count_j)-tep*(n1*(Tao_xx(count_i,count_j+1)-Tao_xx(count_i,count_j-1))/dx+...
%                 n2*(Tao_xx(count_i,count_j+2)-Tao_xx(count_i,count_j-2))/dx+n1*(Tao_xz(count_i+1,count_j)-Tao_xz(count_i-1,count_j))/dz+...
%                 n2*(Tao_xz(count_i+2,count_j)-Tao_xz(count_i-2,count_j))/dz);
%         end
%     end
%     for count_i=pml+1:nz-pml   %+1--对应最小count_i-1，-2--对应最大count_i+2
%         for count_j=pml+1:nx-pml  
%             tep=dt/dens(count_i,count_j);
%             Vz(count_i,count_j)=Vz(count_i,count_j)-tep*(n1*(Tao_xz(count_i,count_j+1)-Tao_xz(count_i,count_j-1))/dx+...
%                 n2*(Tao_xz(count_i,count_j+2)-Tao_xz(count_i,count_j-2))/dx+n1*(Tao_zz(count_i+1,count_j)-Tao_zz(count_i-1,count_j))/dz+...
%                 n2*(Tao_zz(count_i+2,count_j)-Tao_zz(count_i-2,count_j))/dz);
%         end
%     end   
    %%%%%%%%计算上边界速度
    for count_i=1+2:pml
        kz=(pml-count_i)/pml;
        w3=da*kz^M;
        az(count_i,:)=1/(1+0.5*w3*dt);
        bz(count_i,:)=1-0.5*w3*dt;
    end
        %左上角,w1!=0,w3!=0
        for count_j=1+2:pml
            kx=(pml-count_j)/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            %Vx
            Vx_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Vx_1(3:pml,3:pml)+p0(3:pml,3:pml).*(n1*(Tao_xx(3:pml,(3:pml)+1)-Tao_xx(3:pml,(3:pml)-1))/dx+...
                n2*(Tao_xx(3:pml,(3:pml)+2)-Tao_xx(3:pml,(3:pml)-2))/dx));
            Vx_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Vx_3(3:pml,3:pml)+p0(3:pml,3:pml).*(n1*(Tao_xz((3:pml)+1,3:pml)-Tao_xz((3:pml)-1,3:pml))/dz+...
                n2*(Tao_xz((3:pml)+2,3:pml)-Tao_xz((3:pml)-2,3:pml))/dz));
            Vx(3:pml,3:pml)=Vx_1(3:pml,3:pml)+Vx_3(3:pml,3:pml);
            %Vz
            Vz_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Vz_1(3:pml,3:pml)+p0(3:pml,3:pml).*(n1*(Tao_xz(3:pml,(3:pml)+1)-Tao_xz(3:pml,(3:pml)-1))/dx+...
                n2*(Tao_xz(3:pml,(3:pml)+2)-Tao_xz(3:pml,(3:pml)-2))/dx));
            Vz_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Vz_3(3:pml,3:pml)+p0(3:pml,3:pml).*(n1*(Tao_zz((3:pml)+1,3:pml)-Tao_zz((3:pml)-1,3:pml))/dz+...
                n2*(Tao_zz((3:pml)+2,3:pml)-Tao_zz((3:pml)-2,3:pml))/dz));
            Vz(3:pml,3:pml)=Vz_1(3:pml,3:pml)+Vz_3(3:pml,3:pml);
        %中上,w1=0,w3!=0
             %Vx
            Vx_1(3:pml,pml+1:nx-pml)=Vx_1(3:pml,pml+1:nx-pml)+p0(3:pml,pml+1:nx-pml).*(n1*(Tao_xx(3:pml,(pml+1:nx-pml)+1)-Tao_xx(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xx(3:pml,(pml+1:nx-pml)+2)-Tao_xx(3:pml,(pml+1:nx-pml)-2))/dx);
            Vx_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Vx_3(3:pml,pml+1:nx-pml)+p0(3:pml,pml+1:nx-pml).*(n1*(Tao_xz((3:pml)+1,pml+1:nx-pml)-Tao_xz((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_xz((3:pml)+2,pml+1:nx-pml)-Tao_xz((3:pml)-2,pml+1:nx-pml))/dz));
            Vx(3:pml,pml+1:nx-pml)=Vx_1(3:pml,pml+1:nx-pml)+Vx_3(3:pml,pml+1:nx-pml);
            %Vz
            Vz_1(3:pml,pml+1:nx-pml)=Vz_1(3:pml,pml+1:nx-pml)+p0(3:pml,pml+1:nx-pml).*(n1*(Tao_xz(3:pml,(pml+1:nx-pml)+1)-Tao_xz(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xz(3:pml,(pml+1:nx-pml)+2)-Tao_xz(3:pml,(pml+1:nx-pml)-2))/dx);
            Vz_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Vz_3(3:pml,pml+1:nx-pml)+p0(3:pml,pml+1:nx-pml).*(n1*(Tao_zz((3:pml)+1,pml+1:nx-pml)-Tao_zz((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_zz((3:pml)+2,pml+1:nx-pml)-Tao_zz((3:pml)-2,pml+1:nx-pml))/dz));
            Vz(3:pml,pml+1:nx-pml)=Vz_1(3:pml,pml+1:nx-pml)+Vz_3(3:pml,pml+1:nx-pml);
        %右上角，w1!=0,w3!=0
        for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            %Vx
            Vx_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Vx_1(3:pml,nx-pml+1:nx-2)+p0(3:pml,nx-pml+1:nx-2).*(n1*(Tao_xx(3:pml,(nx-pml+1:nx-2)+1)-Tao_xx(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xx(3:pml,(nx-pml+1:nx-2)+2)-Tao_xx(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Vx_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Vx_3(3:pml,nx-pml+1:nx-2)+p0(3:pml,nx-pml+1:nx-2).*(n1*(Tao_xz((3:pml)+1,nx-pml+1:nx-2)-Tao_xz((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_xz((3:pml)+2,nx-pml+1:nx-2)-Tao_xz((3:pml)-2,nx-pml+1:nx-2))/dz));
            Vx(3:pml,nx-pml+1:nx-2)=Vx_1(3:pml,nx-pml+1:nx-2)+Vx_3(3:pml,nx-pml+1:nx-2);
            %Vz
            Vz_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Vz_1(3:pml,nx-pml+1:nx-2)+p0(3:pml,nx-pml+1:nx-2).*(n1*(Tao_xz(3:pml,(nx-pml+1:nx-2)+1)-Tao_xz(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xz(3:pml,(nx-pml+1:nx-2)+2)-Tao_xz(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Vz_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Vz_3(3:pml,nx-pml+1:nx-2)+p0(3:pml,nx-pml+1:nx-2).*(n1*(Tao_zz((3:pml)+1,nx-pml+1:nx-2)-Tao_zz((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_zz((3:pml)+2,nx-pml+1:nx-2)-Tao_zz((3:pml)-2,nx-pml+1:nx-2))/dz));
            Vz(3:pml,nx-pml+1:nx-2)=Vz_1(3:pml,nx-pml+1:nx-2)+Vz_3(3:pml,nx-pml+1:nx-2);
        
    
%     %计算上边界速度
%     for count_i=1+2:pml
%         kz=(pml-count_i)/pml;
%         w3=da*kz^M;
%         az=1/(1+0.5*w3*dt);
%         bz=1-0.5*w3*dt;
%         %左上角,w1!=0,w3!=0
%         for count_j=1+2:pml
%             kx=(pml-count_j)/pml;
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             ro=dt/dens(count_i,count_j);
%             %Vx
%             Vx_1(count_i,count_j)=ax*(bx*Vx_1(count_i,count_j)-ro*(n1*(Tao_xx(count_i,count_j+1)-Tao_xx(count_i,count_j-1))/dx+...
%                 n2*(Tao_xx(count_i,count_j+2)-Tao_xx(count_i,count_j-2))/dx));
%             Vx_3(count_i,count_j)=az*(bz*Vx_3(count_i,count_j)-ro*(n1*(Tao_xz(count_i+1,count_j)-Tao_xz(count_i-1,count_j))/dz+...
%                 n2*(Tao_xz(count_i+2,count_j)-Tao_xz(count_i-2,count_j))/dz));
%             Vx(count_i,count_j)=Vx_1(count_i,count_j)+Vx_3(count_i,count_j);
%             %Vz
%             Vz_1(count_i,count_j)=ax*(bx*Vz_1(count_i,count_j)-ro*(n1*(Tao_xz(count_i,count_j+1)-Tao_xz(count_i,count_j-1))/dx+...
%                 n2*(Tao_xz(count_i,count_j+2)-Tao_xz(count_i,count_j-2))/dx));
%             Vz_3(count_i,count_j)=az*(bz*Vz_3(count_i,count_j)-ro*(n1*(Tao_zz(count_i+1,count_j)-Tao_zz(count_i-1,count_j))/dz+...
%                 n2*(Tao_zz(count_i+2,count_j)-Tao_zz(count_i-2,count_j))/dz));
%             Vz(count_i,count_j)=Vz_1(count_i,count_j)+Vz_3(count_i,count_j);
%         end
%         %中上,w1=0,w3!=0
%         for count_j=pml+1:nx-pml
%             ro=dt/dens(count_i,count_j);
%             %Vx
%             Vx_1(count_i,count_j)=Vx_1(count_i,count_j)-ro*(n1*(Tao_xx(count_i,count_j+1)-Tao_xx(count_i,count_j-1))/dx+...
%                 n2*(Tao_xx(count_i,count_j+2)-Tao_xx(count_i,count_j-2))/dx);
%             Vx_3(count_i,count_j)=az*(bz*Vx_3(count_i,count_j)-ro*(n1*(Tao_xz(count_i+1,count_j)-Tao_xz(count_i-1,count_j))/dz+...
%                 n2*(Tao_xz(count_i+2,count_j)-Tao_xz(count_i-2,count_j))/dz));
%             Vx(count_i,count_j)=Vx_1(count_i,count_j)+Vx_3(count_i,count_j);
%             %Vz
%             Vz_1(count_i,count_j)=Vz_1(count_i,count_j)-ro*(n1*(Tao_xz(count_i,count_j+1)-Tao_xz(count_i,count_j-1))/dx+...
%                 n2*(Tao_xz(count_i,count_j+2)-Tao_xz(count_i,count_j-2))/dx);
%             Vz_3(count_i,count_j)=az*(bz*Vz_3(count_i,count_j)-ro*(n1*(Tao_zz(count_i+1,count_j)-Tao_zz(count_i-1,count_j))/dz+...
%                 n2*(Tao_zz(count_i+2,count_j)-Tao_zz(count_i-2,count_j))/dz));
%             Vz(count_i,count_j)=Vz_1(count_i,count_j)+Vz_3(count_i,count_j);
%         end
%         %右上角，w1!=0,w3!=0
%         for count_j=nx-pml+1:nx-2
%             kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             ro=dt/dens(count_i,count_j);            % 计算系数ro，为时间步长除以密度
%             %Vx
%             Vx_1(count_i,count_j)=ax*(bx*Vx_1(count_i,count_j)-ro*(n1*(Tao_xx(count_i,count_j+1)-Tao_xx(count_i,count_j-1))/dx+...
%                 n2*(Tao_xx(count_i,count_j+2)-Tao_xx(count_i,count_j-2))/dx));  % 计算x方向速度分量的水平导数部分(split-PML方法)
%             Vx_3(count_i,count_j)=az*(bz*Vx_3(count_i,count_j)-ro*(n1*(Tao_xz(count_i+1,count_j)-Tao_xz(count_i-1,count_j))/dz+...
%                 n2*(Tao_xz(count_i+2,count_j)-Tao_xz(count_i-2,count_j))/dz));  % 计算x方向速度分量的垂直导数部分
%             Vx(count_i,count_j)=Vx_1(count_i,count_j)+Vx_3(count_i,count_j);  % 组合得到完整的x方向速度
%             %Vz
%             Vz_1(count_i,count_j)=ax*(bx*Vz_1(count_i,count_j)-ro*(n1*(Tao_xz(count_i,count_j+1)-Tao_xz(count_i,count_j-1))/dx+...
%                 n2*(Tao_xz(count_i,count_j+2)-Tao_xz(count_i,count_j-2))/dx));  % 计算z方向速度分量的水平导数部分
%             Vz_3(count_i,count_j)=az*(bz*Vz_3(count_i,count_j)-ro*(n1*(Tao_zz(count_i+1,count_j)-Tao_zz(count_i-1,count_j))/dz+...
%                 n2*(Tao_zz(count_i+2,count_j)-Tao_zz(count_i-2,count_j))/dz));  % 计算z方向速度分量的垂直导数部分
%             Vz(count_i,count_j)=Vz_1(count_i,count_j)+Vz_3(count_i,count_j);  % 组合得到完整的z方向速度
%         end
%     end    
    %%%%%%%%计算左右边界速度,w1!=0,w3=0
        %左边界 - 处理模型左侧的CPML吸收边界
        for count_j=1+2:pml    % 遍历左边界区域(从第3个点到PML边界)
            kx=(pml-count_j)/pml;  % 计算归一化的位置系数(从边界向内递减)
            w1=da*kx^M;            % 计算CPML衰减系数(距离边界越近衰减越强)
            ax(:,count_j)=1/(1+0.5*w1*dt);  % 计算CPML更新系数ax
            bx(:,count_j)=1-0.5*w1*dt;      % 计算CPML更新系数bx
        end
            %Vx - 更新左边界区域的x方向速度场
            % 1. 使用四阶有限差分计算左边界区域内x方向速度的水平分量
            Vx_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Vx_1(pml+1:nz-pml,3:pml)+p0(pml+1:nz-pml,3:pml).*(n1*(Tao_xx(pml+1:nz-pml,(3:pml)+1)-Tao_xx(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Tao_xx(pml+1:nz-pml,(3:pml)+2)-Tao_xx(pml+1:nz-pml,(3:pml)-2))/dx));  % 应用CPML边界条件
            % 2. 计算x方向速度的垂直分量(Vx_3在左边界没有衰减,因为w3=0)
            Vx_3(pml+1:nz-pml,3:pml)=Vx_3(pml+1:nz-pml,3:pml)+p0(pml+1:nz-pml,3:pml).*(n1*(Tao_xz((pml+1:nz-pml)+1,3:pml)-Tao_xz((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Tao_xz((pml+1:nz-pml)+2,3:pml)-Tao_xz((pml+1:nz-pml)-2,3:pml))/dz);  % 使用四阶差分计算z方向导数
            % 3. 组合两个分量得到完整的x方向速度
            Vx(pml+1:nz-pml,3:pml)=Vx_1(pml+1:nz-pml,3:pml)+Vx_3(pml+1:nz-pml,3:pml);
            %Vz - 更新左边界区域的z方向速度场
            % 1. 使用四阶有限差分计算左边界区域内z方向速度的水平分量
            Vz_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Vz_1(pml+1:nz-pml,3:pml)+p0(pml+1:nz-pml,3:pml).*(n1*(Tao_xz(pml+1:nz-pml,(3:pml)+1)-Tao_xz(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Tao_xz(pml+1:nz-pml,(3:pml)+2)-Tao_xz(pml+1:nz-pml,(3:pml)-2))/dx));  % 应用CPML边界条件
            % 2. 计算z方向速度的垂直分量(Vz_3在左边界没有衰减,因为w3=0)
            Vz_3(pml+1:nz-pml,3:pml)=Vz_3(pml+1:nz-pml,3:pml)+p0(pml+1:nz-pml,3:pml).*(n1*(Tao_zz((pml+1:nz-pml)+1,3:pml)-Tao_zz((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Tao_zz((pml+1:nz-pml)+2,3:pml)-Tao_zz((pml+1:nz-pml)-2,3:pml))/dz);  % 使用四阶差分计算z方向导数
            % 3. 组合两个分量得到完整的z方向速度
            Vz(pml+1:nz-pml,3:pml)=Vz_1(pml+1:nz-pml,3:pml)+Vz_3(pml+1:nz-pml,3:pml);
         %右边界
        for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            %Vx - 更新右边界区域的x方向速度场
            % 1. 使用四阶有限差分计算右边界区域内x方向速度的水平分量
            Vx_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Vx_1(pml+1:nz-pml,nx-pml+1:nx-2)+p0(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Tao_xx(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Tao_xx(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xx(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Tao_xx(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));  % 应用CPML边界条件
            % 2. 计算x方向速度的垂直分量(Vx_3在右边界没有衰减,因为w3=0)
            Vx_3(pml+1:nz-pml,nx-pml+1:nx-2)=Vx_3(pml+1:nz-pml,nx-pml+1:nx-2)+p0(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Tao_xz((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Tao_xz((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_xz((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Tao_xz((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);  % 使用四阶差分计算z方向导数
            % 3. 组合两个分量得到完整的x方向速度
            Vx(pml+1:nz-pml,nx-pml+1:nx-2)=Vx_1(pml+1:nz-pml,nx-pml+1:nx-2)+Vx_3(pml+1:nz-pml,nx-pml+1:nx-2);
            %Vz - 更新右边界区域的z方向速度场
            % 1. 使用四阶有限差分计算右边界区域内z方向速度的水平分量
            Vz_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Vz_1(pml+1:nz-pml,nx-pml+1:nx-2)+p0(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Tao_xz(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Tao_xz(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xz(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Tao_xz(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));  % 应用CPML边界条件
            % 2. 计算z方向速度的垂直分量(Vz_3在右边界没有衰减,因为w3=0)
            Vz_3(pml+1:nz-pml,nx-pml+1:nx-2)=Vz_3(pml+1:nz-pml,nx-pml+1:nx-2)+p0(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Tao_zz((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Tao_zz((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_zz((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Tao_zz((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);  % 使用四阶差分计算z方向导数
            % 3. 组合两个分量得到完整的z方向速度
            Vz(pml+1:nz-pml,nx-pml+1:nx-2)=Vz_1(pml+1:nz-pml,nx-pml+1:nx-2)+Vz_3(pml+1:nz-pml,nx-pml+1:nx-2);
    
%     %计算左右边界速度,w1!=0,w3=0
%     for count_i=pml+1:nz-pml
%         %左边界
%         for count_j=1+2:pml
%             kx=(pml-count_j)/pml;
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             ro=dt/dens(count_i,count_j);
%             %Vx
%             Vx_1(count_i,count_j)=ax*(bx*Vx_1(count_i,count_j)-ro*(n1*(Tao_xx(count_i,count_j+1)-Tao_xx(count_i,count_j-1))/dx+...
%                 n2*(Tao_xx(count_i,count_j+2)-Tao_xx(count_i,count_j-2))/dx));
%             Vx_3(count_i,count_j)=Vx_3(count_i,count_j)-ro*(n1*(Tao_xz(count_i+1,count_j)-Tao_xz(count_i-1,count_j))/dz+...
%                 n2*(Tao_xz(count_i+2,count_j)-Tao_xz(count_i-2,count_j))/dz);
%             Vx(count_i,count_j)=Vx_1(count_i,count_j)+Vx_3(count_i,count_j);
%             %Vz
%             Vz_1(count_i,count_j)=ax*(bx*Vz_1(count_i,count_j)-ro*(n1*(Tao_xz(count_i,count_j+1)-Tao_xz(count_i,count_j-1))/dx+...
%                 n2*(Tao_xz(count_i,count_j+2)-Tao_xz(count_i,count_j-2))/dx));
%             Vz_3(count_i,count_j)=Vz_3(count_i,count_j)-ro*(n1*(Tao_zz(count_i+1,count_j)-Tao_zz(count_i-1,count_j))/dz+...
%                 n2*(Tao_zz(count_i+2,count_j)-Tao_zz(count_i-2,count_j))/dz);
%             Vz(count_i,count_j)=Vz_1(count_i,count_j)+Vz_3(count_i,count_j);
%         end
%         %右边界
%         for count_j=nx-pml+1:nx-2
%             kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             ro=dt/dens(count_i,count_j);
%             %Vx
%             Vx_1(count_i,count_j)=ax*(bx*Vx_1(count_i,count_j)-ro*(n1*(Tao_xx(count_i,count_j+1)-Tao_xx(count_i,count_j-1))/dx+...
%                 n2*(Tao_xx(count_i,count_j+2)-Tao_xx(count_i,count_j-2))/dx));
%             Vx_3(count_i,count_j)=Vx_3(count_i,count_j)-ro*(n1*(Tao_xz(count_i+1,count_j)-Tao_xz(count_i-1,count_j))/dz+...
%                 n2*(Tao_xz(count_i+2,count_j)-Tao_xz(count_i-2,count_j))/dz);
%             Vx(count_i,count_j)=Vx_1(count_i,count_j)+Vx_3(count_i,count_j);
%             %Vz
%             Vz_1(count_i,count_j)=ax*(bx*Vz_1(count_i,count_j)-ro*(n1*(Tao_xz(count_i,count_j+1)-Tao_xz(count_i,count_j-1))/dx+...
%                 n2*(Tao_xz(count_i,count_j+2)-Tao_xz(count_i,count_j-2))/dx));
%             Vz_3(count_i,count_j)=Vz_3(count_i,count_j)-ro*(n1*(Tao_zz(count_i+1,count_j)-Tao_zz(count_i-1,count_j))/dz+...
%                 n2*(Tao_zz(count_i+2,count_j)-Tao_zz(count_i-2,count_j))/dz);
%             Vz(count_i,count_j)=Vz_1(count_i,count_j)+Vz_3(count_i,count_j);
%         end
%     end    
    %%%%%%%%计算下边界速度
    for count_i=nz-pml+1:nz-2
        kz=(count_i-(nz-pml+1))/pml;
        w3=da*kz^M;
        az(count_i,:)=1/(1+0.5*w3*dt);
        bz(count_i,:)=1-0.5*w3*dt;
    end
        %左下角,w1!=0,w3!=0
        for count_j=1+2:pml
            kx=(pml-count_j)/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            %Vx
            Vx_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Vx_1(nz-pml+1:nz-2,3:pml)+p0(nz-pml+1:nz-2,3:pml).*(n1*(Tao_xx(nz-pml+1:nz-2,(3:pml)+1)-Tao_xx(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Tao_xx(nz-pml+1:nz-2,(3:pml)+2)-Tao_xx(nz-pml+1:nz-2,(3:pml)-2))/dx));
            Vx_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Vx_3(nz-pml+1:nz-2,3:pml)+p0(nz-pml+1:nz-2,3:pml).*(n1*(Tao_xz((nz-pml+1:nz-2)+1,3:pml)-Tao_xz((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Tao_xz((nz-pml+1:nz-2)+2,3:pml)-Tao_xz((nz-pml+1:nz-2)-2,3:pml))/dz));
            Vx(nz-pml+1:nz-2,3:pml)=Vx_1(nz-pml+1:nz-2,3:pml)+Vx_3(nz-pml+1:nz-2,3:pml);
            %Vz
            Vz_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Vz_1(nz-pml+1:nz-2,3:pml)+p0(nz-pml+1:nz-2,3:pml).*(n1*(Tao_xz(nz-pml+1:nz-2,(3:pml)+1)-Tao_xz(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Tao_xz(nz-pml+1:nz-2,(3:pml)+2)-Tao_xz(nz-pml+1:nz-2,(3:pml)-2))/dx));
            Vz_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Vz_3(nz-pml+1:nz-2,3:pml)+p0(nz-pml+1:nz-2,3:pml).*(n1*(Tao_zz((nz-pml+1:nz-2)+1,3:pml)-Tao_zz((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Tao_zz((nz-pml+1:nz-2)+2,3:pml)-Tao_zz((nz-pml+1:nz-2)-2,3:pml))/dz));
            Vz(nz-pml+1:nz-2,3:pml)=Vz_1(nz-pml+1:nz-2,3:pml)+Vz_3(nz-pml+1:nz-2,3:pml);
        %中下,w1=0,w3!=0
            %Vx
            Vx_1(nz-pml+1:nz-2,pml+1:nx-pml)=Vx_1(nz-pml+1:nz-2,pml+1:nx-pml)+p0(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Tao_xx(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Tao_xx(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xx(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Tao_xx(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
            Vx_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Vx_3(nz-pml+1:nz-2,pml+1:nx-pml)+p0(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Tao_xz((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Tao_xz((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_xz((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Tao_xz((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
            Vx(nz-pml+1:nz-2,pml+1:nx-pml)=Vx_1(nz-pml+1:nz-2,pml+1:nx-pml)+Vx_3(nz-pml+1:nz-2,pml+1:nx-pml);
            %Vz
            Vz_1(nz-pml+1:nz-2,pml+1:nx-pml)=Vz_1(nz-pml+1:nz-2,pml+1:nx-pml)+p0(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Tao_xz(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Tao_xz(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xz(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Tao_xz(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
            Vz_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Vz_3(nz-pml+1:nz-2,pml+1:nx-pml)+p0(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Tao_zz((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Tao_zz((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_zz((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Tao_zz((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
            Vz(nz-pml+1:nz-2,pml+1:nx-pml)=Vz_1(nz-pml+1:nz-2,pml+1:nx-pml)+Vz_3(nz-pml+1:nz-2,pml+1:nx-pml);
        %右下角，w1!=0,w3!=0
        for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            %Vx
            Vx_1(nz-pml+1:nz-2,nx-pml+1:nx-2)=ax(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bx(nz-pml+1:nz-2,nx-pml+1:nx-2).*Vx_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+p0(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Tao_xx(nz-pml+1:nz-2,(nx-pml+1:nx-2)+1)-Tao_xx(nz-pml+1:nz-2,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xx(nz-pml+1:nz-2,(nx-pml+1:nx-2)+2)-Tao_xx(nz-pml+1:nz-2,(nx-pml+1:nx-2)-2))/dx));
            Vx_3(nz-pml+1:nz-2,nx-pml+1:nx-2)=az(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bz(nz-pml+1:nz-2,nx-pml+1:nx-2).*Vx_3(nz-pml+1:nz-2,nx-pml+1:nx-2)+p0(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Tao_xz((nz-pml+1:nz-2)+1,nx-pml+1:nx-2)-Tao_xz((nz-pml+1:nz-2)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_xz((nz-pml+1:nz-2)+2,nx-pml+1:nx-2)-Tao_xz((nz-pml+1:nz-2)-2,nx-pml+1:nx-2))/dz));
            Vx(nz-pml+1:nz-2,nx-pml+1:nx-2)=Vx_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+Vx_3(nz-pml+1:nz-2,nx-pml+1:nx-2);
            %Vz
            Vz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)=ax(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bx(nz-pml+1:nz-2,nx-pml+1:nx-2).*Vz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+p0(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Tao_xz(nz-pml+1:nz-2,(nx-pml+1:nx-2)+1)-Tao_xz(nz-pml+1:nz-2,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xz(nz-pml+1:nz-2,(nx-pml+1:nx-2)+2)-Tao_xz(nz-pml+1:nz-2,(nx-pml+1:nx-2)-2))/dx));
            Vz_3(nz-pml+1:nz-2,nx-pml+1:nx-2)=az(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bz(nz-pml+1:nz-2,nx-pml+1:nx-2).*Vz_3(nz-pml+1:nz-2,nx-pml+1:nx-2)+p0(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Tao_zz((nz-pml+1:nz-2)+1,nx-pml+1:nx-2)-Tao_zz((nz-pml+1:nz-2)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_zz((nz-pml+1:nz-2)+2,nx-pml+1:nx-2)-Tao_zz((nz-pml+1:nz-2)-2,nx-pml+1:nx-2))/dz));
            Vz(nz-pml+1:nz-2,nx-pml+1:nx-2)=Vz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+Vz_3(nz-pml+1:nz-2,nx-pml+1:nx-2);
        
        
            

%     %计算下边界速度
%     for count_i=nz-pml+1:nz-2
%         kz=(count_i-(nz-pml+1))/pml;
%         w3=da*kz^M;
%         az=1/(1+0.5*w3*dt);
%         bz=1-0.5*w3*dt;
%         %左下角,w1!=0,w3!=0
%         for count_j=1+2:pml
%             kx=(pml-count_j)/pml;
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             ro=dt/dens(count_i,count_j);
%             %Vx
%             Vx_1(count_i,count_j)=ax*(bx*Vx_1(count_i,count_j)-ro*(n1*(Tao_xx(count_i,count_j+1)-Tao_xx(count_i,count_j-1))/dx+...
%                 n2*(Tao_xx(count_i,count_j+2)-Tao_xx(count_i,count_j-2))/dx));
%             Vx_3(count_i,count_j)=az*(bz*Vx_3(count_i,count_j)-ro*(n1*(Tao_xz(count_i+1,count_j)-Tao_xz(count_i-1,count_j))/dz+...
%                 n2*(Tao_xz(count_i+2,count_j)-Tao_xz(count_i-2,count_j))/dz));
%             Vx(count_i,count_j)=Vx_1(count_i,count_j)+Vx_3(count_i,count_j);
%             %Vz
%             Vz_1(count_i,count_j)=ax*(bx*Vz_1(count_i,count_j)-ro*(n1*(Tao_xz(count_i,count_j+1)-Tao_xz(count_i,count_j-1))/dx+...
%                 n2*(Tao_xz(count_i,count_j+2)-Tao_xz(count_i,count_j-2))/dx));
%             Vz_3(count_i,count_j)=az*(bz*Vz_3(count_i,count_j)-ro*(n1*(Tao_zz(count_i+1,count_j)-Tao_zz(count_i-1,count_j))/dz+...
%                 n2*(Tao_zz(count_i+2,count_j)-Tao_zz(count_i-2,count_j))/dz));
%             Vz(count_i,count_j)=Vz_1(count_i,count_j)+Vz_3(count_i,count_j);
%         end
%         %中下,w1=0,w3!=0
%         for count_j=pml+1:nx-pml
%             ro=dt/dens(count_i,count_j);
%             %Vx
%             Vx_1(count_i,count_j)=Vx_1(count_i,count_j)-ro*(n1*(Tao_xx(count_i,count_j+1)-Tao_xx(count_i,count_j-1))/dx+...
%                 n2*(Tao_xx(count_i,count_j+2)-Tao_xx(count_i,count_j-2))/dx);
%             Vx_3(count_i,count_j)=az*(bz*Vx_3(count_i,count_j)-ro*(n1*(Tao_xz(count_i+1,count_j)-Tao_xz(count_i-1,count_j))/dz+...
%                 n2*(Tao_xz(count_i+2,count_j)-Tao_xz(count_i-2,count_j))/dz));
%             Vx(count_i,count_j)=Vx_1(count_i,count_j)+Vx_3(count_i,count_j);
%             %Vz
%             Vz_1(count_i,count_j)=Vz_1(count_i,count_j)-ro*(n1*(Tao_xz(count_i,count_j+1)-Tao_xz(count_i,count_j-1))/dx+...
%                 n2*(Tao_xz(count_i,count_j+2)-Tao_xz(count_i,count_j-2))/dx);
%             Vz_3(count_i,count_j)=az*(bz*Vz_3(count_i,count_j)-ro*(n1*(Tao_zz(count_i+1,count_j)-Tao_zz(count_i-1,count_j))/dz+...
%                 n2*(Tao_zz(count_i+2,count_j)-Tao_zz(count_i-2,count_j))/dz));
%             Vz(count_i,count_j)=Vz_1(count_i,count_j)+Vz_3(count_i,count_j);
%         end
%         %右下角，w1!=0,w3!=0
%         for count_j=nx-pml+1:nx-2
%             kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
%             w1=da*kx^M;
%             ax=1/(1+0.5*w1*dt);
%             bx=1-0.5*w1*dt;
%             ro=dt/dens(count_i,count_j);
%             %Vx
%             Vx_1(count_i,count_j)=ax*(bx*Vx_1(count_i,count_j)-ro*(n1*(Tao_xx(count_i,count_j+1)-Tao_xx(count_i,count_j-1))/dx+...
%                 n2*(Tao_xx(count_i,count_j+2)-Tao_xx(count_i,count_j-2))/dx));
%             Vx_3(count_i,count_j)=az*(bz*Vx_3(count_i,count_j)-ro*(n1*(Tao_xz(count_i+1,count_j)-Tao_xz(count_i-1,count_j))/dz+...
%                 n2*(Tao_xz(count_i+2,count_j)-Tao_xz(count_i-2,count_j))/dz));
%             Vx(count_i,count_j)=Vx_1(count_i,count_j)+Vx_3(count_i,count_j);
%             %Vz
%             Vz_1(count_i,count_j)=ax*(bx*Vz_1(count_i,count_j)-ro*(n1*(Tao_xz(count_i,count_j+1)-Tao_xz(count_i,count_j-1))/dx+...
%                 n2*(Tao_xz(count_i,count_j+2)-Tao_xz(count_i,count_j-2))/dx));
%             Vz_3(count_i,count_j)=az*(bz*Vz_3(count_i,count_j)-ro*(n1*(Tao_zz(count_i+1,count_j)-Tao_zz(count_i-1,count_j))/dz+...
%                 n2*(Tao_zz(count_i+2,count_j)-Tao_zz(count_i-2,count_j))/dz));
%             Vz(count_i,count_j)=Vz_1(count_i,count_j)+Vz_3(count_i,count_j);
%         end
%     end  

    %显示进度
    if(mod(count_t,10)==0)
        disp('time points:');disp(count_t);disp('of');disp('source:');disp(count_s);
        %sprintf('%d %s %d',count_t,'of source:',count_s)%显示到了哪一炮
    end
    %波场快照显示
    if(count_s<=num_s)  %显示所有炮点的波场快照，一直更新到第67炮
        if(mod(count_t,frame_interval)==0)  %每10个时间步长显示一次
            %创建四个子图显示不同的波场分量和数据
            figure(1);
            clf; % 清除之前的所有绘图，防止旧的检波点标记累积
            set(gcf, 'Position', [100 100 1200 900]);
            
            % 子图1: 显示Tao_xx应力场
            subplot(2,2,1);
            imagesc(dx*(1:nx), dz*(1:nz), Tao_xx/10^6);
            colormap(jet);
            colorbar;
            title(['水平应力场 (Txx) - 第', num2str(count_s), '炮, t = ', num2str(count_t*dt, '%.4f'), '秒'], 'FontSize', 12);
            xlabel('水平距离 (m)', 'FontSize', 10);
            ylabel('深度 (m)', 'FontSize', 10);
            axis equal;
            caxis([-0.1, 0.1]);
            
            % 标记震源位置
            hold on;
            plot(dx*med_x, dz*pos_s, 'r*', 'MarkerSize', 10);
            % 删除震源文字标签，只保留红色星号标记
            
            % 标记检波点/标距位置
            for i = 1:num_gauge_points
                rec_pos_plot = pos_s - len_StoR - (i-1) * len_RtoR;
                plot(dx*med_x, dz*rec_pos_plot, 'ko', 'MarkerSize', 6, 'MarkerFaceColor', 'g', 'DisplayName', '检波点标记');
                % 删除标距文字标签，只保留绿色标记点
            end
            
            % 绘制地层情况及井筑位置
            % 定义区域边界
            x_well_left = dx*(med_x-l_cal);
            x_well_right = dx*(med_x+l_cal);
            y_well_top = 0;
            y_well_bottom = dz*nz;
            
            % 绘制地层及其标签
            % 左侧地层 - 地层一（vp2=4500, vs2=2300）
            h_left = rectangle('Position',[0, y_well_top, x_well_left, y_well_bottom], ...
                     'FaceColor',[0.8 0.8 0.6], 'EdgeColor','none', 'FaceAlpha', 0.3);
            text(x_well_left/2, y_well_bottom*0.1, ['地层一 (vp=', num2str(vp2), ',vs=', num2str(vs2), ')'], ...
                 'FontSize', 8, 'HorizontalAlignment', 'center', 'BackgroundColor', [1 1 1 0.7]);
            
            % 中间区域 - 井眼（vp1=1500, vs1=0）
            h_well = rectangle('Position',[x_well_left, y_well_top, x_well_right-x_well_left, y_well_bottom], ...
                     'FaceColor',[0.6 0.9 1], 'EdgeColor','b', 'LineWidth', 2, 'LineStyle', '--', 'FaceAlpha', 0.3);
            text((x_well_left+x_well_right)/2, y_well_bottom*0.2, ['井眼 (vp=', num2str(vp1), ',vs=', num2str(vs1), ')'], ...
                 'FontSize', 8, 'HorizontalAlignment', 'center', 'BackgroundColor', [1 1 1 0.7]);
            
            % 右侧地层 - 地层一（vp2=4500, vs2=2300）
            h_right = rectangle('Position',[x_well_right, y_well_top, dx*nx-x_well_right, y_well_bottom], ...
                     'FaceColor',[0.8 0.8 0.6], 'EdgeColor','none', 'FaceAlpha', 0.3);
            text(x_well_right + (dx*nx-x_well_right)/2, y_well_bottom*0.1, ['地层一 (vp=', num2str(vp2), ',vs=', num2str(vs2), ')'], ...
                 'FontSize', 8, 'HorizontalAlignment', 'center', 'BackgroundColor', [1 1 1 0.7]);
            
            % 井旁侵入带可视化
            % 计算侵入带位置
            invasion_x_start = x_well_right;
            invasion_x_width = dx * Formation_DIter;
            invasion_y_mid = dz * nz/2;
            invasion_y_height = dz * Formation_HIter;
            invasion_y_start = invasion_y_mid - invasion_y_height/2;
            
            % 绘制侵入带矩形
            h_invasion = rectangle('Position',[invasion_x_start, invasion_y_start, invasion_x_width, invasion_y_height], ...
                         'FaceColor',[1 0.6 0.6], 'EdgeColor','r', 'LineWidth', 1.5, 'LineStyle', '-', 'FaceAlpha', 0.5);
            
            % 添加侵入带标签
            text(invasion_x_start + invasion_x_width/2, invasion_y_start + invasion_y_height/2, ...
                 ['侵入带\newline(vp=', num2str(Vpout), ',vs=', num2str(Vsout), ')'], ...
                 'FontSize', 8, 'HorizontalAlignment', 'center', 'Color', 'r', ...
                 'BackgroundColor', [1 1 1 0.7], 'VerticalAlignment', 'middle');
            
            % 绘制PML边界
            % PML边界 - 左侧
            pml_left_x = 0;
            pml_left_width = dx*pml;
            rectangle('Position',[pml_left_x, 0, pml_left_width, dz*nz], 'EdgeColor','m', 'LineWidth', 1.5, 'LineStyle', ':');
            rectangle('Position',[pml_left_x, 0, pml_left_width, dz*nz], ...
                     'EdgeColor','m', 'LineWidth', 1.5, 'LineStyle', ':');
            % 删除PML文字标签，只保留图形标记
            
            % PML边界 - 右侧
            pml_right_x = dx*(nx-pml);
            pml_right_width = dx*pml;
            rectangle('Position',[pml_right_x, 0, pml_right_width, dz*nz], ...
                     'EdgeColor','m', 'LineWidth', 1.5, 'LineStyle', ':');
            % 删除PML文字标签，只保留图形标记
            
            % PML边界 - 上侧
            pml_top_y = 0;
            pml_top_height = dz*pml;
            rectangle('Position',[0, pml_top_y, dx*nx, pml_top_height], ...
                     'EdgeColor','m', 'LineWidth', 1.5, 'LineStyle', ':');
            % 删除PML文字标签，只保留图形标记
            
            % PML边界 - 下侧
            pml_bottom_y = dz*(nz-pml);
            pml_bottom_height = dz*pml;
            rectangle('Position',[0, pml_bottom_y, dx*nx, pml_bottom_height], ...
                     'EdgeColor','m', 'LineWidth', 1.5, 'LineStyle', ':');
            % 删除PML文字标签，只保留图形标记
            
            % 子图2: DAS与检波器数据实时对比图
            subplot(2,2,2);
            hold on;
            
            % 设置横坐标为时间（秒）
            t_range = 1:min(count_t, maxt);
            time_s = t_range * dt; % 单位为秒
            
            % 选择要显示的标距点数量
            show_points = min(num_gauge_points, N); % 显示的点数尽量与DAS和检波器相同
            vertical_space = 1; % 波形之间的垂直间隔
            
            % 绘制水平基准线
            for i = 1:show_points
                % 绘制一条水平基准线
                plot(time_s, zeros(size(time_s)) + i, 'k-', 'LineWidth', 1);
                
                % 添加标距标签
                text(max(time_s)*1.05, i, ['标距 ', num2str(i), ' (', num2str(100+(i-1)*10), '.00m)'], 'FontSize', 8);
            end
            
            % 在同一个图上绘制DAS和检波器数据
            for i = 1:show_points
                % 获取并归一化DAS数据
                das_data = das_strain_rate(i, t_range);
                if max(abs(das_data)) > 0
                    das_data = das_data / max(abs(das_data)) * 0.4; % 缩放幅度为0.4
                end
                
                % 获取并归一化检波器数据
                detector_data = X(i, t_range);
                if max(abs(detector_data)) > 0
                    detector_data = detector_data / max(abs(detector_data)) * 0.4; % 缩放幅度为0.4
                end
                
                % 绘制DAS应变率（红色）
                plot(time_s, das_data + i, 'r-', 'LineWidth', 1.2);
                
                % 绘制检波器数据（黑色）
                plot(time_s, detector_data + i, 'k-', 'LineWidth', 1.2);
            end
            
            % 标记当前时间点
            current_time = count_t * dt;
            if current_time <= max(time_s)
                plot([current_time, current_time], [0, show_points+1], 'b--', 'LineWidth', 1.5);
            end
            
            % 添加图例
            legend('', 'DAS应变率 (红色)', '传统检波器 (黑色)', 'Location', 'southeast');
            
            % 设置图表属性
            yticks(1:show_points);
            yticklabels(1:show_points);
            xlabel('时间 (秒)', 'FontSize', 10);
            ylabel('检波点编号', 'FontSize', 10);
            title(['第', num2str(count_s), '个点源的标距与检波器偏移图'], 'FontSize', 12);
            grid on;
            xlim([0, max(time_s)]);
            ylim([0, show_points+1]);
            
            % 添加波形图例说明
            text(max(time_s)*0.7, show_points*0.2, 'DAS应变率 (红色)', 'Color', 'r', 'FontSize', 9);
            text(max(time_s)*0.7, show_points*0.1, '传统检波器 (黑色)', 'Color', 'k', 'FontSize', 9);
            
            % 子图3: 显示DAS应变率变化图
            subplot(2,2,3);
            % 只显示当前时间步之前的数据
            t_range = 1:min(count_t, maxt);
            time_ms = t_range * dt * 1000; % 转换为毫秒
            
            hold on;
            % 绘制每个标距点的应变率变化
            for i = 1:num_gauge_points
                % 归一化并偏移每一个标距点的数据
                normalized_data = das_strain_rate(i, t_range);
                if max(abs(normalized_data)) > 0
                    normalized_data = normalized_data / max(abs(normalized_data));
                end
                % 添加偏移以便于查看 - 从下往上为1,2,3...8
                plot(time_ms, normalized_data + i*2, 'LineWidth', 1.5); % 使用i*2增大波形间距
            end
            
            % 标记当前时间点
            current_time_ms = count_t * dt * 1000;
            if current_time_ms <= max(time_ms)
                plot([current_time_ms, current_time_ms], [0, num_gauge_points*2+1], 'r--', 'LineWidth', 1.5);
                text(current_time_ms, 0.2, '当前时间', 'Color', 'r', 'FontSize', 8, 'HorizontalAlignment', 'center');
            end
            
            % 添加标记
            yticks((1:num_gauge_points)*2);
            yticklabels(1:num_gauge_points); % 修改为从下往上为1,2,3...8
            xlabel('时间 (ms)', 'FontSize', 10);
            ylabel('标距点编号', 'FontSize', 10);
            title('DAS应变率变化', 'FontSize', 12);
            grid on;
            xlim([0, max(time_ms)]);
            ylim([0, num_gauge_points*2+1]); % 增加Y轴范围以容纳增大的波形间隔
            
            % 子图4: 显示检波器接收速度图
            subplot(2,2,4);
            
            % 彻底清除当前子图以避免干扰
            cla reset; % 使用reset选项完全重置子图状态
            
            % 添加空白背景覆盖之前的内容
            fill([0 max(time_ms) max(time_ms) 0], [0 0 N*2+1 N*2+1], 'w', 'EdgeColor', 'none');
            hold on;
            % 绘制每个检波点的接收速度
            for i = 1:N
                % 计算每个检波点的位置
                rec_pos = pos_s - len_StoR - (i-1) * len_RtoR;
                
                % 当前时间步前的速度分量历史
                vx_hist = zeros(1, length(t_range));
                vz_hist = zeros(1, length(t_range));
                
                % 从X数组获取检波器数据，X是在主循环中收集的实际波场数据
                % X中存储了每个时间步的实际波场值
                % 检索该检波点在所有时间步中的波场数据
                for t = 1:length(t_range)
                    if t <= count_t
                        % 直接使用X数组中收集的实际波场数据
                        % X的每一行对应一个检波点，每一列对应一个时间步
                        vz_hist(t) = X(i, t); % 使用实际波场中的Z方向速度
                        vx_hist(t) = X(i, t) * 0.5; % X方向速度通常较小
                    else
                        vz_hist(t) = 0;
                        vx_hist(t) = 0;
                    end
                end
                
                % 绘制z方向速度（主要分量）
                % 归一化并偏移
                if max(abs(vz_hist)) > 0
                    vz_norm = vz_hist / max(abs(vz_hist));
                else
                    vz_norm = vz_hist;
                end
                
                % 使用line函数代替plot函数，更严格的控制没有标记
                % line函数与plot不同，默认不会显示标记点
                line(time_ms, vz_norm + i*2, 'Color', [0 0 0.8], 'LineWidth', 1.5, 'LineStyle', '-');
            end
            
            % 标记当前时间点
            if current_time_ms <= max(time_ms)
                plot([current_time_ms, current_time_ms], [0, N*2+1], 'r--', 'LineWidth', 1.5);
                text(current_time_ms, 0.2, '当前时间', 'Color', 'r', 'FontSize', 8, 'HorizontalAlignment', 'center');
            end
            
            % 添加标记
            yticks((1:N)*2);
            yticklabels(1:N); % 修改为从下往上为1,2,3...8
            xlabel('时间 (ms)', 'FontSize', 10);
            ylabel('检波点编号', 'FontSize', 10);
            title('检波器Z方向速度', 'FontSize', 12);
            grid on;
            xlim([0, max(time_ms)]);
            ylim([0, N*2+1]); % 增加Y轴范围以容纳增大的波形间距
            
            % 调整全局标题
            sgtitle(['声波测井模拟 - 第', num2str(count_s), '炮, 时间步: ', num2str(count_t), '/', num2str(maxt)], 'FontSize', 14, 'FontWeight', 'bold');
            
            % 调整子图间距
            set(gcf, 'Position', [100 100 1200 900]);
            drawnow;
            
            % 标记震源和检波点位置
            hold on;
            plot(dx*med_x, dz*pos_s, 'r*', 'MarkerSize', 10);
            
            % 标记检波点/标距位置
            for i = 1:num_gauge_points
                rec_pos_plot = pos_s - len_StoR - (i-1) * len_RtoR;
                plot(dx*med_x, dz*rec_pos_plot, 'ko', 'MarkerSize', 6, 'MarkerFaceColor', 'g');
                % 移除检波点文字标签，只保留标记点
            end
            
            % 添加总标题 - 使用sgtitle代替suptitle
            sgt = sgtitle(['声波测井模拟 - 波场传播快照 (时间步: ', num2str(count_t), '/总步数: ', num2str(maxt), ')']);
            set(sgt, 'FontSize', 14, 'FontWeight', 'bold');
            
            drawnow;  % 立即更新图像显示
            % 可选: 添加小暂停以便查看
            % pause(0.01);
            
            % 可选: 保存帧以创建动画
            % F(count_F) = getframe(gcf);
            % count_F = count_F + 1;
        end
    end
    % 采集DAS数据 - 计算应变率作为唯一的检波方式
    for i = 1:num_gauge_points
        % 计算检波点位置
        rec_pos = pos_s - len_StoR - (i-1) * len_RtoR;
        
        % 计算标距的两个端点位置
        gauge_half_length_grid = round(gauge_length/(2*dz));
        gauge_start = max(1, rec_pos - gauge_half_length_grid);
        gauge_end = min(nz, rec_pos + gauge_half_length_grid);
        
        % DAS主要测量纵向形变，使用Vz（z方向速度）计算应变率
        % 应变率的变化直接替代传统检波器的相位信息
        v_plus = Vz(gauge_start, med_x);
        v_minus = Vz(gauge_end, med_x);
        
        % 计算应变率：两端速度差除以标距长度
        actual_distance = (gauge_end - gauge_start) * dz;  % 实际标距长度（米）
        das_strain_rate(i, count_t) = (v_plus - v_minus) / actual_distance;
    end
    
    % 保存原始检波点数据用于后续对比
    onep=zeros(N,1);
    cc=1;
    %从下往上取值，第一道到第N道
    for count_j=pos_s-len_StoR:-len_RtoR:pos_s-len_StoR-(N-1)*len_RtoR
        % 使用水平方向速度(Vz)而非应力场(Tao_xx)
        % 检波器常规是测量位移/速度，而非应力
        onep(cc)=Vz(count_j,med_x);
        cc=cc+1;
    end
    X(:,count_t)=onep;
    %X,1--N
end
    %一炮结束，保存DAS数据和原始检波点数据用于对比
    disp('计算完炮数：');
    disp(count_s);
    
    % 保存DAS数据
    for i = 1:num_gauge_points
        data(count_s, (i-1)*maxt+1:i*maxt) = das_strain_rate(i, :);
    end
    
    % 保存原始数据用于对比（可选，保存到单独的变量）
    for count_i=1:1:N
        original_data_all(count_s,(count_i-1)*maxt+1:count_i*maxt)=X(count_i,:);
    end

end
 toc 
% mov = close(mov);
%save qingxie3 data
%movie(F,1,10);%播放,1次，每秒10帧

% 保留数据处理，但删除额外的图形显示
% 数据仍然保存在data和original_data_all变量中，以便后续使用

% 计算每个标距点的第一波到达时间（仅计算，不显示）
first_arrivals = zeros(num_gauge_points, 1);
time_vec = (1:maxt) * dt;

% 使用STA/LTA方法检测到达时间
for i = 1:num_gauge_points
    % 获取当前标距点的数据
    current_data = data(count_s, (i-1)*maxt+1:i*maxt);
    
    % 计算信号能量
    signal_energy = current_data.^2;
    
    % STA/LTA参数
    sta_win = 5;   % 短时窗口长度
    lta_win = 30;  % 长时窗口长度
    threshold_ratio = 3.0;  % 触发阈值
    
    % 初始化STA/LTA
    sta = zeros(maxt, 1);
    lta = zeros(maxt, 1);
    
    % 填充初始窗口
    for j = 1:lta_win
        if j <= sta_win
            sta(j) = mean(signal_energy(1:j));
        else
            sta(j) = mean(signal_energy(j-sta_win+1:j));
        end
        lta(j) = mean(signal_energy(1:j));
    end
    
    % 检测第一波到达
    for j = lta_win+1:maxt
        % 短时窗口平均
        sta(j) = mean(signal_energy(j-sta_win+1:j));
        % 长时窗口平均
        lta(j) = mean(signal_energy(j-lta_win+1:j));
        
        % 避免除零错误
        if lta(j) < 1e-10
            lta(j) = 1e-10;
        end
        
        % 计算比值并检查是否超过阈值
        ratio = sta(j) / lta(j);
        if ratio > threshold_ratio && j > lta_win + 10 % 避免初始噪声触发
            first_arrivals(i) = time_vec(j);
            break;
        end
    end
end
%% ======= 数据重组与波形汇总图显示 =======
% 创建波形汇总数组 - 检波器数据
all_detector_waveforms = zeros(num_s * N, maxt);
% 创建波形汇总数组 - DAS数据
all_das_waveforms = zeros(num_s * num_gauge_points, maxt);

% 重组数据，按深度连续排列所有波形
fprintf('开始重组数据，按深度连续排列所有波形...\n');
for s = 1:num_s
    % 检波器数据重组
    for d = 1:N
        depth_index = (s-1)*N + d;
        all_detector_waveforms(depth_index, :) = original_data_all(s, (d-1)*maxt+1:d*maxt);
    end
    
    % DAS数据重组
    for g = 1:num_gauge_points
        depth_index = (s-1)*num_gauge_points + g;
        all_das_waveforms(depth_index, :) = data(s, (g-1)*maxt+1:g*maxt);
    end
end
fprintf('数据重组完成，共%d个检波器波形和%d个DAS波形\n', num_s*N, num_s*num_gauge_points);

%% 创建一个函数来绘制波形汇总图，减少代码重复
function plotWaveformSummary(data, total_points, time_vector, spacing_factor, color_style, title_text)
    figure('Position', [100, 100, 800, 1200]);
    depth_labels = 1:30:total_points; % 选择部分深度标签显示
    
    hold on;
    for i = 1:total_points
        % 归一化波形并添加偏移
        data_trace = data(i,:);
        max_val = max(abs(data_trace));
        if max_val > 0  % 添加这个检查避免除以0或接近0的值
            normalized_wave = data_trace / max_val + (total_points-i)*spacing_factor;
            plot(time_vector, normalized_wave, color_style, 'LineWidth', 0.5);
        else
            % 如果数据全为0，仍然绘制一条水平线表示该位置
            plot(time_vector, zeros(size(time_vector)) + (total_points-i)*spacing_factor, color_style, 'LineWidth', 0.5);
        end
    end
    
    % 添加深度标签
    ytick_pos = sort((total_points-depth_labels)*spacing_factor); % 确保递增顺序
    yticks(ytick_pos);
    yticklabels(fliplr(depth_labels)); % 翻转标签以匹配正确的位置
    xlabel('时间 (μs)', 'FontSize', 12);
    ylabel('接收点', 'FontSize', 12);
    title(title_text, 'FontSize', 14);
    grid on;
    hold off;
end

%% 可视化函数用于绘制比较图
function plotComparisonWaveforms(data1, data2, total_points1, total_points2, time_vector, spacing_factor, step_size)
    % 选择部分数据点进行对比，避免图形过于复杂
    selected_indices = 1:step_size:min(total_points1, total_points2);
    
    figure('Position', [100, 100, 1000, 800]);
    hold on;
    
    % 绘制第一组数据
    for i = 1:length(selected_indices)
        idx = selected_indices(i);
        if idx <= total_points1
            data_trace = data1(idx,:);
            max_val = max(abs(data_trace));
            if max_val > 0  % 添加这个检查避免除以0或接近0的值
                normalized_wave = data_trace / max_val + (length(selected_indices)-i)*spacing_factor;
                plot(time_vector, normalized_wave, 'k-', 'LineWidth', 0.5);
            else
                % 如果数据全为0，仍然绘制一条水平线
                plot(time_vector, zeros(size(time_vector)) + (length(selected_indices)-i)*spacing_factor, 'k-', 'LineWidth', 0.5);
            end
        end
    end
    
    % 绘制第二组数据
    for i = 1:length(selected_indices)
        idx = selected_indices(i);
        if idx <= total_points2
            data_trace = data2(idx,:);
            max_val = max(abs(data_trace));
            if max_val > 0  % 添加这个检查避免除以0或接近0的值
                normalized_wave = data_trace / max_val + (length(selected_indices)-i)*spacing_factor;
                plot(time_vector, normalized_wave, 'r--', 'LineWidth', 0.5);
            else
                % 如果数据全为0，仍然绘制一条水平线
                plot(time_vector, zeros(size(time_vector)) + (length(selected_indices)-i)*spacing_factor, 'r--', 'LineWidth', 0.5);
            end
        end
    end
    
    % 添加标签
    tick_indices = 1:5:length(selected_indices);
    ytick_pos = sort((length(selected_indices)-tick_indices)*spacing_factor); % 确保递增顺序
    yticks(ytick_pos);
    yticklabels(fliplr(selected_indices(tick_indices))); % 翻转标签以匹配正确的位置
    xlabel('时间 (μs)', 'FontSize', 12);
    ylabel('接收点', 'FontSize', 12);
    title('检波器数据(黑色实线)与DAS数据(红色虚线)对比', 'FontSize', 14);
    legend('检波器数据', 'DAS数据');
    grid on;
    hold off;
end

%% ======= 使用新函数绘制波形汇总图 =======
time_vector = (1:maxt)*dt*1e6; % 转换为微秒
spacing_factor = 1.5; % 波形间隔因子

% 绘制检波器数据汇总图
plotWaveformSummary(all_detector_waveforms, num_s*N, time_vector, spacing_factor, 'k-', '传统检波器数据汇总图 (所有砲点的所有接收点)');

% 绘制DAS数据汇总图
plotWaveformSummary(all_das_waveforms, num_s*num_gauge_points, time_vector, spacing_factor, 'r-', 'DAS应变率数据汇总图 (所有砲点的所有标距点)');

% 绘制比较图
step_size = 8; % 每8个点选择一个，与新的检波器数量一致
plotComparisonWaveforms(all_detector_waveforms, all_das_waveforms, num_s*N, num_s*num_gauge_points, time_vector, spacing_factor, step_size);

%% ======= 保存数据 =======
% 保存所有重要数据到MAT文件，包括重组后的数据
% 定义保存路径
save_path = '/Volumes/纪乃暄2号/博0/声波/DAS 声波正演/zhengyan526/run_data/das_simulation_results.mat';
save(save_path, 'data', 'original_data_all', 'first_arrivals', 'dt', 'maxt', 'N', 'num_s', 'gauge_centers', 'all_detector_waveforms', 'all_das_waveforms');
fprintf('数据已保存到: %s\n', save_path);

% 绘制波形汇总图的函数，可用于后续分析
fprintf('\n程序执行完成。添加了数据重组和波形汇总图显示功能。\n');
fprintf('您可以通过加载保存的数据文件，随时查看和分析所有波形。\n');