% 测试振幅显示模式的通用性
% 这个脚本用于验证振幅模式在检波器和DAS中都能正常工作

clear; clc;

% 添加当前目录到路径，确保能找到函数
addpath(pwd);

% 创建测试数据
test_data = randn(100, 1) * 0.5;  % 随机测试数据
test_data(1:20) = test_data(1:20) * 5;  % 第一道振幅更大

fprintf('测试振幅显示模式的通用性\n');
fprintf('================================\n\n');

% 测试所有6种振幅模式
for mode = 1:6
    fprintf('测试模式 %d:\n', mode);
    
    % 测试第一道（应该有特殊处理）
    result1 = apply_amplitude_mode(test_data, 1, mode, 20.0);
    
    % 测试第二道（正常处理）
    result2 = apply_amplitude_mode(test_data, 2, mode, 20.0);
    
    % 显示结果统计
    fprintf('  第一道: 最大值=%.3f, 最小值=%.3f, 标准差=%.3f\n', ...
            max(result1), min(result1), std(result1));
    fprintf('  第二道: 最大值=%.3f, 最小值=%.3f, 标准差=%.3f\n', ...
            max(result2), min(result2), std(result2));
    
    % 检查模式6的特殊行为
    if mode == 6
        ratio = max(abs(result1)) / max(abs(result2));
        fprintf('  第一道与第二道振幅比值: %.2f (期望约为20)\n', ratio);
    end
    
    fprintf('\n');
end

fprintf('测试完成！所有模式都应该能在检波器和DAS绘图中正常工作。\n');
