% DAS绘图程序快速启动脚本
% 提供交互式界面选择绘图模式

%% 清理工作空间
clear; clc; close all;

%% 显示欢迎信息
fprintf('========================================\n');
fprintf('    DAS与检波器绘图程序快速启动\n');
fprintf('========================================\n\n');

%% 检查数据文件
fprintf('正在检查数据文件...\n');

% 检查检波器数据
receiver_file = 'FDTD_SeismicLogging_20250729_152710.mat';
receiver_exists = exist(receiver_file, 'file');

% 检查DAS数据
das_file = 'das_simulation_results.mat';
das_exists = exist(das_file, 'file');

if receiver_exists
    fprintf('✓ 检波器数据文件: %s\n', receiver_file);
else
    fprintf('✗ 检波器数据文件缺失: %s\n', receiver_file);
end

if das_exists
    fprintf('✓ DAS数据文件: %s\n', das_file);
else
    fprintf('✗ DAS数据文件缺失: %s\n', das_file);
end

%% 根据文件存在情况提供选项
fprintf('\n可用的绘图模式:\n');

available_modes = [];
mode_descriptions = {};

if receiver_exists
    available_modes(end+1) = 1;
    mode_descriptions{end+1} = '1 - 仅绘制检波器波形';
    fprintf('  1 - 仅绘制检波器波形\n');
end

if das_exists
    available_modes(end+1) = 2;
    mode_descriptions{end+1} = '2 - 仅绘制DAS波形';
    fprintf('  2 - 仅绘制DAS波形\n');
end

if receiver_exists && das_exists
    available_modes(end+1) = 3;
    mode_descriptions{end+1} = '3 - DAS与检波器叠加对比';
    fprintf('  3 - DAS与检波器叠加对比 (黑色=检波器, 红色=DAS)\n');
end

if isempty(available_modes)
    fprintf('错误: 没有找到必要的数据文件！\n');
    fprintf('请确保以下文件存在于当前目录:\n');
    fprintf('  - %s (检波器数据)\n', receiver_file);
    fprintf('  - %s (DAS数据)\n', das_file);
    return;
end

%% 用户选择模式
fprintf('\n请选择绘图模式 [');
fprintf('%d', available_modes);
fprintf(']: ');

while true
    user_choice = input('');
    
    if ismember(user_choice, available_modes)
        selected_mode = user_choice;
        break;
    else
        fprintf('无效选择！请输入 [');
        fprintf('%d', available_modes);
        fprintf(']: ');
    end
end

%% 显示选择的模式
fprintf('\n您选择了: %s\n', mode_descriptions{available_modes == selected_mode});

%% 询问是否使用默认配置
fprintf('\n是否使用默认配置? [Y/n]: ');
use_default = input('', 's');

if isempty(use_default) || strcmpi(use_default, 'y') || strcmpi(use_default, 'yes')
    use_default_config = true;
else
    use_default_config = false;
end

%% 设置配置
if use_default_config
    fprintf('使用默认配置...\n');
    
    % 默认配置
    shot_numbers = [5, 10, 20, 30, 35, 40];
    num_channels = 21;
    channel_start = 1;
    
else
    fprintf('\n=== 自定义配置 ===\n');
    
    % 炮点选择
    fprintf('请输入要处理的炮点编号 (例如: [5, 10, 20, 30]): ');
    shot_input = input('');
    if isempty(shot_input)
        shot_numbers = [5, 10, 20, 30, 35, 40];
    else
        shot_numbers = shot_input;
    end
    
    % 通道/检波器数量
    fprintf('请输入要显示的通道/检波器数量 [默认21]: ');
    num_input = input('');
    if isempty(num_input)
        num_channels = 21;
    else
        num_channels = num_input;
    end
    
    % 起始通道/检波器
    fprintf('请输入起始通道/检波器编号 [默认1]: ');
    start_input = input('');
    if isempty(start_input)
        channel_start = 1;
    else
        channel_start = start_input;
    end
end

%% 生成配置并运行
fprintf('\n正在生成配置并启动绘图程序...\n');

% 创建临时配置文件
temp_config_file = 'temp_config.m';
fid = fopen(temp_config_file, 'w');

fprintf(fid, '%% 临时生成的配置文件\n');
fprintf(fid, 'plot_mode = %d;\n', selected_mode);
fprintf(fid, 'data_filename = ''%s'';\n', receiver_file);
fprintf(fid, 'das_data_filename = ''%s'';\n', das_file);
fprintf(fid, 'shot_numbers = [');
fprintf(fid, '%d ', shot_numbers);
fprintf(fid, '];\n');

fprintf(fid, 'config.num_receivers_to_show = %d;\n', num_channels);
fprintf(fid, 'config.receiver_start = %d;\n', channel_start);
fprintf(fid, 'config.das_num_channels_to_show = %d;\n', num_channels);
fprintf(fid, 'config.das_channel_start = %d;\n', channel_start);

% 其他默认配置
fprintf(fid, 'config.trace_spacing = 1.5;\n');
fprintf(fid, 'config.amplitude_scale = 0.8;\n');
fprintf(fid, 'config.show_first_arrivals = false;\n');
fprintf(fid, 'config.time_range = [0, 0.004];\n');
fprintf(fid, 'config.fill_positive = true;\n');
fprintf(fid, 'config.fill_color = [0.8, 0.2, 0.2];\n');
fprintf(fid, 'config.das_trace_spacing = 1.5;\n');
fprintf(fid, 'config.das_amplitude_scale = 0.8;\n');
fprintf(fid, 'config.das_fill_positive = true;\n');
fprintf(fid, 'config.das_fill_color = [0.2, 0.2, 0.8];\n');
fprintf(fid, 'config.receiver_line_color = [0, 0, 0];\n');
fprintf(fid, 'config.das_line_color = [1, 0, 0];\n');
fprintf(fid, 'config.receiver_line_width = 1.2;\n');
fprintf(fid, 'config.das_line_width = 1.0;\n');
fprintf(fid, 'config.dpi = 400;\n');
fprintf(fid, 'config.figure_size = [1200, 900];\n');
fprintf(fid, 'amplitude_mode = 6;\n');
fprintf(fid, 'first_trace_amplitude_factor = 20.0;\n');

fclose(fid);

%% 显示即将执行的配置
fprintf('\n=== 配置摘要 ===\n');
fprintf('绘图模式: %d (%s)\n', selected_mode, mode_descriptions{available_modes == selected_mode});
fprintf('炮点编号: [');
fprintf('%d ', shot_numbers);
fprintf(']\n');
fprintf('显示通道数: %d\n', num_channels);
fprintf('起始通道: %d\n', channel_start);

%% 询问是否继续
fprintf('\n是否开始绘图? [Y/n]: ');
proceed = input('', 's');

if isempty(proceed) || strcmpi(proceed, 'y') || strcmpi(proceed, 'yes')
    fprintf('\n开始绘图...\n');
    fprintf('========================================\n');
    
    % 运行配置
    run(temp_config_file);
    
    % 运行主程序的核心部分
    % 注意: 这里需要将huitu_best.m的主要逻辑复制过来，或者修改huitu_best.m使其可以接受外部配置
    fprintf('请手动运行 huitu_best.m 或将临时配置复制到主程序中\n');
    fprintf('临时配置已保存到: %s\n', temp_config_file);
    
else
    fprintf('已取消绘图操作\n');
end

%% 清理临时文件
if exist(temp_config_file, 'file')
    delete(temp_config_file);
end

fprintf('\n程序结束\n');
